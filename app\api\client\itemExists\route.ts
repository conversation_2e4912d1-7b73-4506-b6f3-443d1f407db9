import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ClientModel } from "@/app/models/Client";
import { JC_Utils_Business } from "@/app/Utils";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// Check if Client exists
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        const result = await JC_Utils_Business.sqlItemExists(ClientModel, id);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
