"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../../components/JC_Button/JC_Button";
import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import JC_List from "../../components/JC_List/JC_List";
import JC_PageContentContainer from "../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_Breadcrumbs from "../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_Tooltip from "../../components/JC_Tooltip/JC_Tooltip";
import { MaterialModel } from "../../models/Material";
import { MethodModel } from "../../models/Method";
import { JC_Utils } from "../../Utils";
import { TooltipPositionEnum } from "../../enums/TooltipPosition";

export default function Page_Materials() {
    const router = useRouter();
    const [methods, setMethods] = useState<MethodModel[]>([]);
    const [initialised, setInitialised] = useState<boolean>(false);

    // Load methods for tooltips
    useEffect(() => {
        async function loadMethods() {
            try {
                const methodsResult = await MethodModel.GetList();
                setMethods(methodsResult.ResultList);
                setInitialised(true);
            } catch (error) {
                console.error("Error loading methods:", error);
                JC_Utils.showToastError("Failed to load methods.");
                setInitialised(true);
            }
        }

        loadMethods();
    }, []);

    // This function is no longer needed as we're using Ex_MethodNames from the backend
    // Keeping it as a fallback in case Ex_MethodNames is not populated
    function getMethodNames(material: MaterialModel): string[] {
        // If Ex_MethodNames is populated, use it
        if (material.Ex_MethodNames && material.Ex_MethodNames.length > 0) {
            return material.Ex_MethodNames;
        }

        // Fallback to calculating method names from IDs
        try {
            const methodIds = JSON.parse(material.MethodIdsListJson);
            return methodIds.map((methodId: string) => {
                const method = methods.find(m => m.Id === methodId);
                return method ? method.Name : 'Unknown Method';
            });
        } catch (error) {
            console.error("Error parsing method IDs:", error);
            return [];
        }
    }

    // Handle edit material
    function handleEditMaterial(id: string) {
        router.push(`/settings/materials/edit/${id}`);
    }

    // Handle new material
    function handleNewMaterial() {
        // Check if methods exist
        if (methods.length === 0) {
            JC_Utils.showToastWarning("You must create a Method first!");
            return;
        }
        router.push("/settings/materials/edit/new");
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Settings", path: "/settings" },
                    { label: "Materials", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Materials" />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <>
                        <JC_List
                            service={MaterialModel.GetList}
                            headers={[
                                { label: "Name",       sortKey: "Name" },
                                { label: "Cost",       sortKey: "CostPrice",       hideOnTeenyTiny: true },
                                { label: "Markup",     sortKey: "PercentMarkup",   hideOnTiny: true },
                                { label: "Sale",       sortKey: "SalesPrice" },
                                { label: "Methods",    sortKey: "Ex_MethodsCount", hideOnTiny: true },
                                { label: "Created At", sortKey: "CreatedAt",       hideOnSmall: true }
                            ]}
                            defaultSortKey="Name"
                            defaultSortAsc={true}
                            row={(material: MaterialModel) => (
                                <tr
                                    key={material.Id}
                                    onClick={() => handleEditMaterial(material.Id)}
                                >
                                    <td>{material.Name}</td>
                                    <td>${material.CostPrice.toFixed(2)}</td>
                                    <td>{`${material.PercentMarkup.toFixed(2)}%`}</td>
                                    <td>${material.SalesPrice.toFixed(2)}</td>
                                    <td className={styles.methodCell}>
                                        {(() => {
                                            try {
                                                const methodNames = getMethodNames(material);

                                                if (material.Ex_MethodsCount === 0) {
                                                    return 0;
                                                }

                                                return (
                                                    <>
                                                        <div className={styles.methodCount}>
                                                            {material.Ex_MethodsCount}
                                                        </div>
                                                        <JC_Tooltip
                                                            content={
                                                                <div className={styles.methodTooltip}>
                                                                    {methodNames.map((name, index) => (
                                                                        <div key={index} className={styles.methodName}>
                                                                            {name}
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            }
                                                            position={TooltipPositionEnum.Top}
                                                            absoluteFillSpace={true}
                                                        />
                                                    </>
                                                );
                                            } catch (error) {
                                                console.error("Error parsing method IDs:", error);
                                                return 0;
                                            }
                                        })()}
                                    </td>
                                    <td>{new Date(material.CreatedAt).toLocaleDateString() + ' ' + new Date(material.CreatedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true}).replace(' ', '')}</td>
                                </tr>
                            )}
                        />
                        <JC_Button
                            text="+"
                            onClick={handleNewMaterial}
                            isSecondary
                            isCircular
                            overrideClass={styles.addButton}
                        />
                    </>
                )}
            </JC_PageContentContainer>
        </div>
    );
}
