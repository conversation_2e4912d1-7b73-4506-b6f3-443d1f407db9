import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ProductBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// Get Products by MaterialId
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const materialId = params.get("materialId");

        if (!materialId) {
            return NextResponse.json({ error: "Missing 'materialId' parameter" }, { status: 400 });
        }

        const result = await ProductBusiness.GetByMaterialId(materialId);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
