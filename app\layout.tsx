import styles from "./layout.module.scss";
import type { Metadata } from "next";
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Inter, Special_Elite, K2D } from 'next/font/google';
import { ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import JC_Header from "./components/JC_Header/JC_Header";
import JC_Footer from "./components/JC_Footer/JC_Footer";
import JC_BackgroundGlow from "./components/JC_BackgroundGlow/JC_BackgroundGlow";
import { auth } from "./auth";
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { JC_Utils_PageConfig } from './utils/pageConfig';
import AdminRequiredMessage from './adminRequiredMessage';

// Site Metadata
export const metadata: Metadata = {
    title: process.env.NAME,
    description: "Admin site for N-Grave.",
    robots: {
        index: false,
        follow: false,
    },
};

// Font
const inter = Inter({ subsets: ["latin"], variable: '--font-inter' });
const kaushanScript = Special_Elite({ weight: "400", subsets: ["latin"], variable: '--font-kaushan-script' });
const k2dFont = K2D({ weight: "400", subsets: ["latin"], variable: '--font-k2d' });

// Site Root
export default async function Layout_Root(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Get the current path to check if it's a demo page and if header/footer should be hidden
    const headersList = headers();
    const path = headersList.get('x-pathname') || headersList.get('x-invoke-path') || '';
    const isLoginRegisterPage = path === '/loginRegister' || path.startsWith('/loginRegister/');

    // Get session for the SessionProvider
    const session = await auth();

    // If user is not logged in and not on a public path, redirect to login page
    if (!session && !JC_Utils_PageConfig.isPublicPath(path)) {
        redirect('/loginRegister');
    }

    // If user is logged in but not an admin, show admin required message and redirect to home
    const showAdminRequiredMessage = session && !session.user.IsAdmin && !JC_Utils_PageConfig.isPublicPath(path);

    // Redirect non-admin users to home page if they try to access a non-public page
    if (showAdminRequiredMessage && path !== '/') {
        redirect('/');
    }

    // Hide header and footer for login page, pages in the config list, and when showing admin required message
    const hideHeaderFooter = JC_Utils_PageConfig.shouldHideHeaderFooter(path) || isLoginRegisterPage || showAdminRequiredMessage;

    return (
        <html lang="en">
            <body className={`${styles.rootMainContainer} ${inter.variable} ${kaushanScript.variable} ${k2dFont.variable}`} id="rootMainContainer">
                <JC_BackgroundGlow />

                {/* Show header if not on a page that should hide header/footer */}
                {!hideHeaderFooter && <JC_Header />}

                <div className={styles.pageContainer}>
                    <SessionProvider session={session}>
                        {showAdminRequiredMessage ? <AdminRequiredMessage /> : _.children}
                    </SessionProvider>
                </div>

                {/* Show footer if not on a page that should hide header/footer */}
                {!hideHeaderFooter && <JC_Footer />}

                <ToastContainer />
                <SpeedInsights />
            </body>
        </html>
    );
}
