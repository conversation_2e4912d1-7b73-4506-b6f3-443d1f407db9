import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ClientModel } from "@/app/models/Client";
import { JC_Utils_Business } from "@/app/Utils";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// Get all Clients
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();

        const { searchParams } = new URL(request.url);
        const paging = JC_Utils_Business.getPagingFromParams(searchParams, ClientModel);

        let result = await JC_Utils_Business.sqlGetList<ClientModel>(
            ClientModel,
            undefined, // no where clause - get all clients
            paging
        );

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
