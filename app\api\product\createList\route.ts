import { NextRequest, NextResponse } from "next/server";
import { ProductModel } from "@/app/models/Product";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const products:ProductModel[] = await request.json();
        for (const product of products) {
            await JC_Utils_Business.sqlCreate(ProductModel, product);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
