@import './global';

.mainContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    left: 0;
    top: 0;
    text-align: center;
    z-index: 9999;
    background-color: $offBlack;

    > div {
        transform: translateY(-60px);
    }

    .logoContainer {
        margin-bottom: 70px;

        .logo {
            width: 380px;
            height: auto;
            max-width: 100%;
        }
    }

    h1 {
        font-family: var(--font-k2d);
        font-size: 2.5rem;
        color: $primaryColor;
        margin-top: 0;
        margin-bottom: 1rem;
    }

    p {
        font-size: 1.2rem;
        color: $white;
        margin-bottom: 2rem;
    }
}
