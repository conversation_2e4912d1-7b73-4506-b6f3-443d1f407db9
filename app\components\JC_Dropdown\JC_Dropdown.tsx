"use client";

import styles from "./JC_Dropdown.module.scss";
import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import Image from "next/image";
import JC_Checkbox from "../JC_Checkbox/JC_Checkbox";
import { JC_FieldOption } from "@/app/models/ComponentModels/JC_FieldOption";
import { DropdownTypeEnum } from "@/app/enums/DropdownType";
import { JC_Utils } from "@/app/Utils";
import JC_DropdownExpandedList from "./JC_DropdownExpandedList/JC_DropdownExpandedList";

export default function JC_Dropdown(
  _: Readonly<{
    overrideClass?: string;
    type: DropdownTypeEnum;
    label?: string;
    placeholder?: string;
    options: JC_FieldOption[];
    onOptionMouseOver?: (optionId: string) => void;
    onOptionMouseOut?: (optionId: string) => void;
    selectedOptionId?: string;
    removeSelectedInDropdown?: boolean;
    enableSearch?: boolean;
    onSelection: (newOptionId: string) => void;
    validate?: (value: string | number | undefined) => string;
    disabled?: boolean;
    readOnly?: boolean;
  }>
) {
  // - STATE - //

  const [thisInputId] = useState<string>(JC_Utils.generateGuid());
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [searchBoxText, setSearchBoxText] = useState<string>();
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  });
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(
    null
  );

  // Refs
  const buttonRef = useRef<HTMLDivElement>(null);

  // Effect to create portal container
  useEffect(() => {
    if (typeof document !== "undefined") {
      // Make sure we have a portal container
      setPortalContainer(document.body);
      console.log("JC_Dropdown - Portal container set to document.body");
    }
  }, []);

  // Function to update dropdown position
  const updateDropdownPosition = () => {
    if (dropdownOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom,
        left: rect.left,
        width: rect.width,
      });
    }
  };

  // Effect to position dropdown when it opens
  useEffect(() => {
    if (dropdownOpen) {
      console.log("JC_Dropdown - Dropdown opened, updating position");
      updateDropdownPosition();
    }
  }, [dropdownOpen]);

  // Effect to handle window resize
  useEffect(() => {
    if (dropdownOpen) {
      window.addEventListener("resize", updateDropdownPosition);
      window.addEventListener("scroll", updateDropdownPosition);

      // Add click outside handler with timeout
      const handleClickOutside = (event: MouseEvent) => {
        // Add a timeout to delay the execution of the click outside handler
        setTimeout(() => {
          const target = event.target as Node;
          const dropdownElement = document.querySelector(
            '[class*="dropdownPortal"]'
          );

          console.log("Click outside handler triggered (with delay)");

          // Check if the click is on a dropdown option (using CSS modules class pattern)
          const targetElement = target as Element;
          const isDropdownOption =
            targetElement.className &&
            (targetElement.className.includes("dropdownOption") ||
              targetElement.closest('[class*="dropdownOption"]'));

          if (isDropdownOption) {
            console.log("Click is on a dropdown option, not closing dropdown");
            return; // Don't close the dropdown if clicking on an option
          }

          // Close only if click is outside both the button and dropdown
          if (
            buttonRef.current &&
            !buttonRef.current.contains(target) &&
            (!dropdownElement || !dropdownElement.contains(target))
          ) {
            console.log("Closing dropdown from click outside");
            setDropdownOpen(false);
            setSearchBoxText("");
          }
        }, 100); // 100ms delay to allow option selection to process first
      };

      document.addEventListener("mousedown", handleClickOutside);

      return () => {
        window.removeEventListener("resize", updateDropdownPosition);
        window.removeEventListener("scroll", updateDropdownPosition);
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [dropdownOpen]);

  // - INITIALISE - //

  // Effect to update dropdown position when search text changes
  useEffect(() => {
    if (dropdownOpen) {
      // Small delay to allow the filtered list to render
      setTimeout(updateDropdownPosition, 50);
    }
  }, [searchBoxText]);

  let optionsList = _.options;
  if (_.removeSelectedInDropdown && _.selectedOptionId) {
    optionsList = optionsList.filter((o) => o.OptionId !== _.selectedOptionId);
  }
  if (!JC_Utils.stringNullOrEmpty(searchBoxText)) {
    optionsList = optionsList.filter((o) =>
      JC_Utils.searchMatches(searchBoxText!, o.Label)
    );
  }

  // - BUILD - //

  // Dropdown Option Content
  function buildOptionContent(option?: JC_FieldOption, isMain?: boolean) {
    if (!option) {
      return (
        <React.Fragment>
          {!JC_Utils.stringNullOrEmpty(_.placeholder) && (
            <div
              className={styles.optionLabel}
              style={{
                flexGrow: 1,
                fontSize: "13px",
                textAlign: "left",
              }}
            >
              {_.placeholder}
            </div>
          )}
        </React.Fragment>
      );
    }

    return (
      <React.Fragment>
        {!JC_Utils.stringNullOrEmpty(option.IconName) && (
          <Image
            src={`/icons/${option.IconName}.svg`}
            width={100}
            height={100}
            className={styles.optionIcon}
            alt={`${option.IconName} icon`}
          />
        )}

        {!JC_Utils.stringNullOrEmpty(option.Label) && (
          <div
            className={styles.optionLabel}
            style={{
              flexGrow: 1,
              fontSize: "13px",
              textAlign: "left",
            }}
          >
            {option.Label}
          </div>
        )}

        {_.type == DropdownTypeEnum.Multi && !isMain && (
          <div
            className={styles.checkbox}
            style={{
              position: "absolute",
              top: "50%",
              transform: "translateY(-50%)",
              right: "10px",
            }}
          >
            <JC_Checkbox
              checked={option.Selected ?? false}
              onChange={() => _.onSelection(option.OptionId)}
            />
          </div>
        )}
      </React.Fragment>
    );
  }

  // - HANDLES - //

  // Function removed as we're now calling onSelection directly from the portal

  // - MAIN - //

  return (
    <div
      className={`${
        !JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ""
      }`}
    >
      {/* Label */}
      {!JC_Utils.stringNullOrEmpty(_.label) && (
        <div className={styles.label}>
          {_.label}
          {_.validate != null &&
            !JC_Utils.stringNullOrEmpty(_.validate(_.selectedOptionId)) && (
              <span className={styles.errorSpan}>
                {_.validate(_.selectedOptionId)}
              </span>
            )}
        </div>
      )}

      {/* Dropdown */}
      <div className={styles.mainContainer}>
        {/* Selected Option */}
        <div
          ref={buttonRef}
          className={`${styles.mainButton} ${
            _.readOnly ? styles.readOnly : ""
          }`}
          onClick={() => {
            if (_.readOnly) return; // Don't open dropdown if readOnly
            if (!dropdownOpen) {
              setDropdownOpen(true);
              setTimeout(
                () =>
                  document
                    .getElementById(`dropdown-input-${thisInputId}`)
                    ?.focus(),
                20
              );
            } else {
              setSearchBoxText("");
              setDropdownOpen(false);
            }
          }}
        >
          {/* Selected Option */}
          {buildOptionContent(
            optionsList.find((o) => o.OptionId === _.selectedOptionId),
            true
          )}

          {/* Chevron */}
          <Image
            className={styles.chevronIcon}
            src="/icons/Chevron.svg"
            style={dropdownOpen ? { rotate: "180deg", top: "20%" } : {}}
            width={50}
            height={50}
            alt="Bag"
          />

          {/* Search */}
          {_.enableSearch && dropdownOpen && (
            <input
              id={`dropdown-input-${thisInputId}`}
              className={styles.searchBox}
              type="text"
              placeholder="Search..."
              value={searchBoxText || ""}
              onChange={(event) => {
                // Update search text which will filter the options list
                setSearchBoxText(event.target.value);
                // Force position update after a short delay to ensure the dropdown repositions if needed
                setTimeout(updateDropdownPosition, 50);
              }}
              onKeyDown={(event) => {
                // Handle Escape key - close dropdown
                if (event.key === "Escape") {
                  setSearchBoxText("");
                  setDropdownOpen(false);
                }
                // Handle Enter key - select first item if available
                else if (event.key === "Enter" && optionsList.length > 0) {
                  const firstOption = optionsList[0];
                  console.log(
                    "JC_Dropdown - Enter key pressed, selecting first option:",
                    firstOption.OptionId
                  );
                  _.onSelection(firstOption.OptionId);
                  setTimeout(() => {
                    setSearchBoxText("");
                    setDropdownOpen(false);
                  }, 50);
                }
              }}
            />
          )}
        </div>

        {/* Portal elements */}
        {dropdownOpen && portalContainer && (
          <>
            {/* Dropdown List - Rendered in Portal */}
            {createPortal(
              <JC_DropdownExpandedList
                options={optionsList}
                selectedOptionId={_.selectedOptionId}
                type={_.type}
                position={dropdownPosition}
                onOptionMouseOver={_.onOptionMouseOver}
                onOptionMouseOut={_.onOptionMouseOut}
                onSelection={(optionId) => {
                  console.log(
                    "JC_Dropdown - Portal onSelection called with optionId:",
                    optionId
                  );
                  // Call parent onSelection directly to ensure the event is processed
                  _.onSelection(optionId);
                  // Close the dropdown after a short delay to ensure the selection is processed
                  setTimeout(() => {
                    setSearchBoxText("");
                    setDropdownOpen(false);
                  }, 50);
                }}
                buildOptionContent={(option) => buildOptionContent(option)}
              />,
              portalContainer
            )}
          </>
        )}
      </div>
    </div>
  );
}
