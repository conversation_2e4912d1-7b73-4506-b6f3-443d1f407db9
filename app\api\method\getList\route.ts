import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { MethodModel } from "@/app/models/Method";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all Methods
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();

        const { searchParams } = new URL(request.url);
        const paging = JC_Utils_Business.getPagingFromParams(searchParams, MethodModel);

        let result = await JC_Utils_Business.sqlGetList<MethodModel>(
            MethodModel,
            undefined, // no where clause - get all methods
            paging
        );

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
