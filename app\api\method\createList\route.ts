import { NextRequest, NextResponse } from "next/server";
import { MethodModel } from "@/app/models/Method";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const methods:MethodModel[] = await request.json();
        for (const method of methods) {
            await JC_Utils_Business.sqlCreate(MethodModel, method);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
