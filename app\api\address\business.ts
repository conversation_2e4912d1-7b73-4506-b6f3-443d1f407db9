import { sql } from "@vercel/postgres";
import { AddressModel } from "../../models/Address";

export class AddressBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetByUserId(userId:string) {
        return (await sql<AddressModel>`
            SELECT "Id",
                   "UserId",
                   "Line1",
                   "Line2",
                   "City",
                   "Postcode",
                   "Country",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Address"
            WHERE "UserId" = ${userId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

}
