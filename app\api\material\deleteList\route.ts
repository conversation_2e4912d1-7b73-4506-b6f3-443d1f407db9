import { NextRequest, NextResponse } from "next/server";
import { MaterialModel } from "@/app/models/Material";
import { JC_Utils_Business } from "@/app/Utils";

export async function DELETE(request: NextRequest) {
    try {
        const ids:string[] = await request.json();
        for (const id of ids) {
            await JC_Utils_Business.sqlDelete(MaterialModel, id);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
