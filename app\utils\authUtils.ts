import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/auth';

/**
 * Authentication utility class for N-Grave
 * Implements authentication and admin checks for API routes
 */
export class JC_Utils_Auth {
  /**
   * List of API routes that don't require authentication
   * Only includes login/register related endpoints
   */
  static publicApiRoutes = [
    '/api/auth',                          // NextAuth routes
    '/api/user/getByToken',               // Password reset
    '/api/user/resetPassword',            // Password reset
    '/api/user/triggerResetPasswordToken' // Password reset request
  ];

  /**
   * Middleware function to check if a user is authenticated and is admin for API routes
   * @param request The NextRequest object
   * @returns NextResponse with error or null if authenticated
   */
  static async checkApiAuth(request: NextRequest): Promise<NextResponse | null> {
    const path = request.nextUrl.pathname;

    // Skip check for public API routes
    if (this.isPublicApiRoute(path)) {
      return null;
    }

    // Special case for user registration (PUT method)
    if (path === '/api/user' && request.method === 'PUT') {
      return null;
    }

    try {
      // Get the session
      const session = await auth();

      // If no session, user is not logged in
      if (!session) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // If user is not admin, return forbidden
      if (!session.user.IsAdmin) {
        return NextResponse.json(
          { error: 'Admin access required' },
          { status: 403 }
        );
      }

      // User is authenticated and is admin
      return null;
    } catch (error) {
      console.error('Error checking API auth:', error);
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      );
    }
  }

  /**
   * Higher-order function to wrap API route handlers with authentication check
   * @param handler The API route handler function
   * @returns A wrapped handler function that checks authentication before executing
   */
  static withApiAuth<T>(
    handler: (request: NextRequest, ...args: any[]) => Promise<T>
  ): (request: NextRequest, ...args: any[]) => Promise<T> {
    return async (request: NextRequest, ...args: any[]) => {
      // Check authentication
      const authResponse = await this.checkApiAuth(request);

      // If auth check returned a response, return it (unauthorized/forbidden)
      if (authResponse) {
        return authResponse as unknown as T;
      }

      // Otherwise, proceed with the handler
      return handler(request, ...args);
    };
  }

  /**
   * Check if an API route is public (doesn't require authentication)
   * @param path The API route path
   * @returns Boolean indicating if the path is public
   */
  static isPublicApiRoute(path: string): boolean {
    // If path is empty, return false (require authentication by default)
    if (!path) return false;

    // Check if the path exactly matches or starts with any of the paths in the list
    return this.publicApiRoutes.some(publicPath =>
      path === publicPath ||
      path.startsWith(`${publicPath}/`)
    );
  }
}
