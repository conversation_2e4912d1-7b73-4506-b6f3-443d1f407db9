"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../../components/JC_Button/JC_Button";
import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import JC_List from "../../components/JC_List/JC_List";
import JC_PageContentContainer from "../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_Breadcrumbs from "../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import { MethodModel } from "../../models/Method";
import { JC_Utils } from "../../Utils";

export default function Page_Methods() {
    const router = useRouter();
    const [methods, setMethods] = useState<MethodModel[]>([]);
    const [initialised, setInitialised] = useState<boolean>(false);

    // Load methods on page load
    useEffect(() => {
        // Load methods from API
        async function loadMethods() {
            try {
                const result = await MethodModel.GetList();
                setMethods(result.ResultList);
                setInitialised(true);
            } catch (error) {
                console.error("Error loading methods:", error);
                JC_Utils.showToastError("Failed to load methods.");
                setInitialised(true);
            }
        }

        loadMethods();
    }, []);

    // Handle edit method
    function handleEditMethod(id: string) {
        router.push(`/settings/methods/edit/${id}`);
    }

    // Handle new method
    function handleNewMethod() {
        router.push("/settings/methods/edit/new");
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Settings", path: "/settings" },
                    { label: "Methods", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Methods" />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <>
                        <JC_List
                            items={methods}
                            headers={[
                                { label: "Name",              sortKey: "Name" },
                                { label: "Cost",              sortKey: "CostPrice",     hideOnTeenyTiny: true },
                                { label: "Markup",            sortKey: "PercentMarkup", hideOnTiny: true },
                                { label: "Sale",              sortKey: "SalesPrice" },
                                { label: "Cost ($/second)",   sortKey: "CostPerSecond", hideOnTiny: true },
                                { label: "Created At",        sortKey: "CreatedAt",     hideOnSmall: true }
                            ]}
                            defaultSortKey="Name"
                            defaultSortAsc={true}
                            row={(method) => (
                                <tr
                                    key={method.Id}
                                    onClick={() => handleEditMethod(method.Id)}
                                >
                                    <td>{method.Name}</td>
                                    <td>${method.CostPrice.toFixed(2)}</td>
                                    <td>{`${method.PercentMarkup.toFixed(2)}%`}</td>
                                    <td>${method.SalesPrice.toFixed(2)}</td>
                                    <td>${method.CostPerSecond.toFixed(2)}</td>
                                    <td>{new Date(method.CreatedAt).toLocaleDateString() + ' ' + new Date(method.CreatedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true}).replace(' ', '')}</td>
                                </tr>
                            )}
                        />
                        <JC_Button
                            text="+"
                            onClick={handleNewMethod}
                            isSecondary
                            isCircular
                            overrideClass={styles.addButton}
                        />
                    </>
                )}
            </JC_PageContentContainer>
        </div>
    );
}
