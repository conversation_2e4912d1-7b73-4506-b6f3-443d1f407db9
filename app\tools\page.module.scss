@import '../global';

.mainContainer {
    @include fullWidthMainContainerStyles;

    .headerContainer {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .toolsContainer {
        margin-top: 100px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        gap: 30px;

        .formatListButton {
            min-width: 200px;
            height: 50px;
            font-size: 18px;
        }

        .excelUploadButton {
            min-width: 200px;
            height: 50px;
            font-size: 18px;
            margin-top: 200px;
        }

        .outputSection {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;

            .outputTitle {
                font-size: 28px !important;
                margin-top: 0 !important;
                margin-bottom: 10px;
            }

            .outputBox {
                width: 200px;
                height: 200px;
                background-color: $offBlack;
                border: 2px solid $primaryColor;
                border-radius: $smallBorderRadius;
                padding: 15px;
                overflow: auto;
                margin-top: 20px;
                @include containerShadow;
                transition: all 0.3s ease;

                &.hasContent {
                    width: auto;
                    height: auto;
                    max-width: 80%;
                }

                pre {
                    margin: 0;
                    color: $secondaryColor;
                    white-space: pre-wrap;
                    word-break: break-word;
                    font-size: 16px;
                }

                .placeholderText {
                    color: $darkGrey;
                    font-style: italic;
                    text-align: center;
                    font-size: 16px;
                }
            }
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .toolsContainer {
            .formatListButton,
            .excelUploadButton {
                min-width: 180px;
                height: 45px;
                font-size: 16px;
            }

            .outputSection {
                .outputTitle {
                    font-size: 24px !important;
                }

                .outputBox {
                    width: 180px;
                    height: 180px;

                    pre, .placeholderText {
                        font-size: 15px;
                    }
                }
            }
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        .toolsContainer {
            .formatListButton,
            .excelUploadButton {
                min-width: 160px;
                height: 40px;
                font-size: 14px;
            }

            .outputSection {
                .outputTitle {
                    font-size: 20px !important;
                }

                .outputBox {
                    width: 160px;
                    height: 160px;

                    pre, .placeholderText {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
