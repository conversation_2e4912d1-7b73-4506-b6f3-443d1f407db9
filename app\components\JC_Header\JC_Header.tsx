import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import J<PERSON>_Button from "../JC_Button/JC_Button";

import { auth } from "@/app/auth";

export default async function JC_Header() {
    // Still get the session to display user name if logged in
    const session = await auth();

    return (
        <React.Fragment>
            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">
                {/* Logo + Account */}
                <div className={styles.logoAccountContainer}>
                    {/* Logo - always clickable */}
                    <Link href="/">
                        <Image
                            src="/logos/Main.png"
                            width={800}
                            height={193}
                            className={styles.logo}
                            alt={`${process.env.NAME} logo`}
                        />
                    </Link>

                    {/* Account */}
                    <div className={`${styles.checkoutAccountContainer}`}>
                        {/* Show login/register link or account button based on session */}
                        {!session?.user ? (
                            <Link href="/loginRegister" className={styles.loginRegisterContainer}>
                                <div className={styles.loginRegisterText}>Login/Register</div>
                            </Link>
                        ) : (
                            <JC_Button
                                linkToPage="account"
                                text={session.user.FirstName || "Account"}
                                iconName="User"
                            />
                        )}
                    </div>
                </div>
            </div>
        </React.Fragment>
    );
}
