@import '../../../../global';

.mainContainer {
    @include editPageMainContainerStyles;

    .pageWrapper {
        width: auto;
        display: flex;
        flex-direction: column;
    }

    .threeDotsMenuContainer {
        position: relative;
    }

    .headerContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .formContainer {
        width: 100%;
        margin-top: 20px;
    }

    .priceFieldsRow {
        display: flex;
        flex-direction: row;
        column-gap: 30px;
        align-items: flex-start;
        width: 100%;
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .headerContainer {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
    }
}
