"use client"

import styles from "./JC_AffectedProductsModal.module.scss";
import { useState, useEffect } from "react";
import JC_Modal from "../../../../components/JC_Modal/JC_Modal";
import <PERSON><PERSON>_<PERSON>ton from "../../../../components/JC_Button/JC_Button";
import J<PERSON>_Spinner from "../../../../components/JC_Spinner/JC_Spinner";
import JC_Dropdown from "../../../../components/JC_Dropdown/JC_Dropdown";
import { DropdownTypeEnum } from "../../../../enums/DropdownType";
import { JC_FieldOption } from "../../../../models/ComponentModels/JC_FieldOption";
import { ProductModel } from "../../../../models/Product";
import { MethodModel } from "../../../../models/Method";
import Image from "next/image";

interface JC_AffectedProductsModalProps {
    isOpen: boolean;
    onCancel: () => void;
    onSave: (updatedProducts: ProductModel[]) => void;
    affectedProducts: ProductModel[];
    availableMethods: MethodModel[];
    isLoading?: boolean;
}

export default function JC_AffectedProductsModal({
    isOpen,
    onCancel,
    onSave,
    affectedProducts,
    availableMethods,
    isLoading = false
}: JC_AffectedProductsModalProps) {
    const [updatedProducts, setUpdatedProducts] = useState<ProductModel[]>([]);

    // Initialize updatedProducts when affectedProducts changes
    useEffect(() => {
        if (affectedProducts.length > 0) {
            const products = affectedProducts.map(product => ({
                ...product,
                MethodId: availableMethods.length > 0 ? availableMethods[0].Id : ""
            }));
            setUpdatedProducts(products);
        }
    }, [affectedProducts, availableMethods]);

    // Handle method selection for a product
    const handleMethodChange = (productId: string, methodId: string) => {
        setUpdatedProducts(prev =>
            prev.map(product =>
                product.Id === productId
                    ? { ...product, MethodId: methodId }
                    : product
            )
        );
    };

    // Handle save button click
    const handleSave = () => {
        onSave(updatedProducts);
        onCancel();
    };

    return (
        <JC_Modal
            isOpen={isOpen}
            onCancel={onCancel}
            title="Update Products"
        >
            <div className={styles.modalContent}>
                <JC_Spinner overrideClass={`${styles.loadingSpinner} ${!isLoading ? styles.hidden : ""}`} />

                <div className={isLoading ? styles.hidden : ""}>
                    <div className={styles.introText}>
                        The following products use methods that are no longer available on this material.<br/>
                        Please select a new method for each product:
                    </div>

                    <div className={styles.productsTableContainer}>
                        <table className={styles.productsTable}>
                            <tbody>
                                {updatedProducts.map(product => (
                                    <tr key={product.Id} className={styles.productRow}>
                                        <td className={styles.productCell}>
                                            <div className={styles.productContainer}>
                                                <div className={styles.productInfo}>
                                                    <div className={styles.productName}>{product.Name}</div>
                                                    <div className={styles.productDetails}>
                                                        Client: {product.Ex_ClientName} |
                                                        Dimensions: {Math.round(product.DimensionX)}mm × {Math.round(product.DimensionY)}mm
                                                    </div>
                                                </div>

                                                <div className={styles.methodSelection}>
                                                    <div className={styles.currentMethodCell}>
                                                        {product.Ex_MethodName}
                                                    </div>
                                                    <div className={styles.arrowCell}>
                                                        <Image
                                                            src="/icons/Chevron.svg"
                                                            width={20}
                                                            height={10}
                                                            alt="Chevron"
                                                            className={styles.arrow}
                                                        />
                                                    </div>
                                                    <div className={styles.newMethodCell}>
                                                        <JC_Dropdown
                                                            type={DropdownTypeEnum.Default}
                                                            options={availableMethods.map(method => ({
                                                                OptionId: method.Id,
                                                                Label: method.Name
                                                            } as JC_FieldOption))}
                                                            selectedOptionId={product.MethodId}
                                                            enableSearch={true}
                                                            onSelection={(methodId) => handleMethodChange(product.Id, methodId)}
                                                            overrideClass={styles.dropdown}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    <div className={styles.buttonsContainer}>
                        <JC_Button text="Cancel" onClick={onCancel} />
                        <JC_Button text="Save Changes" isSecondary onClick={handleSave} />
                    </div>
                </div>
            </div>
        </JC_Modal>
    );
}
