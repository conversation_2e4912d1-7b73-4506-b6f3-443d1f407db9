import { NextRequest, NextResponse } from "next/server";
import { UserPersistedDataBusiness } from "../business";

export async function DELETE(request: NextRequest) {
    try {
        const idCodePairs:{id:string, code:string}[] = await request.json();
        await UserPersistedDataBusiness.DeleteList(idCodePairs);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
