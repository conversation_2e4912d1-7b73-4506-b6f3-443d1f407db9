import { NextRequest, NextResponse } from "next/server";
import { MethodModel } from "@/app/models/Method";
import { JC_Utils_Business } from "@/app/Utils";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const ids:string[] = await request.json();
        for (const id of ids) {
            await JC_Utils_Business.sqlDelete(MethodModel, id);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
