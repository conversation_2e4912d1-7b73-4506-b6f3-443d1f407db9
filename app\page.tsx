"use client"

import styles from "./page.module.scss";
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { JC_Utils, JC_Utils_NGrave } from "./Utils";
import { LocalStorageKeyEnum } from "./enums/LocalStorageKey";
import Link from "next/link";
import { useFooter } from "./components/JC_Footer/JC_FooterContext";
import J<PERSON>_Button from "./components/JC_Button/JC_Button";
import JC_PageContentContainer from "./components/JC_PageContentContainer/JC_PageContentContainer";

export default function Page_Home() {
    const session = useSession();
    const { closeMenu } = useFooter();

    // IF just logged in, show "Welcome"
    useEffect(() => {
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome) == "1" && session.data != null) {
            JC_Utils.showToastSuccess(`Welcome ${session.data?.user.FirstName}!`);
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "0");
        }
    }, []);

    // Handle format client data button click
    const handleFormatClientData = async () => {
        // Play a random sound
        JC_Utils.playRandomSound();

        // Format clipboard data
        await JC_Utils_NGrave.formatClientData();
    };

    return (
        <div className={styles.mainContainer}>
            <JC_PageContentContainer hasBorder={false}>
                <div className={styles.tilesGrid}>
                    <Link href="/clients" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Clients</div>
                    </Link>
                    <Link href="/products" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Products</div>
                    </Link>
                    <Link href="/products/edit/new" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Add Product</div>
                    </Link>
                </div>
                <div className={styles.tilesGrid} style={{ marginTop: '40px' }}>
                    <Link href="/settings" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Settings</div>
                    </Link>
                </div>
            </JC_PageContentContainer>
        </div>
    );
}
