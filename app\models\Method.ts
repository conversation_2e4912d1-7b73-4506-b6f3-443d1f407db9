import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class MethodModel extends _Base implements _ModelRequirements {

    static tableName: string = "Method";
    static apiRoute: string = "method";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    static cacheMinutes_get = 10080; // 1 week
    static cacheMinutes_getList = 10080; // 1 week

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static async Get(id: string) {
        return await JC_Get<MethodModel>(MethodModel, MethodModel.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${MethodModel.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?: JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<MethodModel>(MethodModel, `${MethodModel.apiRoute}/getList`, paging, undefined, abortSignal);
    }
    static async GetAll() {
        return await JC_GetRaw<MethodModel[]>(`${MethodModel.apiRoute}/all`, {});
    }
    static async Create(data: MethodModel) {
        return await JC_Put<MethodModel>(MethodModel, MethodModel.apiRoute, data);
    }
    static async CreateList(dataList: MethodModel[]) {
        return await JC_PutRaw<MethodModel[]>(`${MethodModel.apiRoute}/createList`, dataList, undefined, "Method");
    }
    static async Update(data: MethodModel) {
        return await JC_Post<MethodModel>(MethodModel, MethodModel.apiRoute, data);
    }
    static async UpdateList(dataList: MethodModel[]) {
        return await JC_PostRaw<MethodModel[]>(`${MethodModel.apiRoute}/updateList`, dataList, undefined, "Method");
    }
    static async Delete(id: string) {
        return await JC_Delete(MethodModel, MethodModel.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${MethodModel.apiRoute}/deleteList`, { ids }, undefined, "Method");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    CostPrice: number;
    PercentMarkup: number;
    SalesPrice: number;
    IsSalesPriceReadOnlyOn: boolean;
    CostPerSecond: number;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        // Add extended field definitions here if needed
    ];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<MethodModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        this.CostPrice = 0;
        this.PercentMarkup = 0;
        this.SalesPrice = 0;
        this.IsSalesPriceReadOnlyOn = false;
        this.CostPerSecond = 0;
        Object.assign(this, init);

        // Ensure number fields are actually numbers
        this.CostPrice = Number(this.CostPrice);
        this.PercentMarkup = Number(this.PercentMarkup);
        this.SalesPrice = Number(this.SalesPrice);
        this.IsSalesPriceReadOnlyOn = Boolean(this.IsSalesPriceReadOnlyOn);
        this.CostPerSecond = Number(this.CostPerSecond);
    }

    static getKeys() {
        return Object.keys(new MethodModel());
    }

    static jcFieldTypeforField(fieldName: keyof MethodModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "Name":
                return FieldTypeEnum.Text;
            case "CostPrice":
                return FieldTypeEnum.Number;
            case "PercentMarkup":
                return FieldTypeEnum.Number;
            case "SalesPrice":
                return FieldTypeEnum.Number;
            case "IsSalesPriceReadOnlyOn":
                return FieldTypeEnum.Custom;
            case "CostPerSecond":
                return FieldTypeEnum.Number;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}
