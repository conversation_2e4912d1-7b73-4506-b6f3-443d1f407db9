import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { UserPersistedDataModel } from "@/app/models/UserPersistedData";
import { UserPersistedDataBusiness } from "./business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" and "Code", or by "UserId" and "Code", or by "UserId", or get all
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const code = params.get("code");
        const userId = params.get("userId");

        let result;
        if (id && code) {
            result = await UserPersistedDataBusiness.Get(id, code);
        } else if (userId && code) {
            result = await UserPersistedDataBusiness.GetByUserIdAndCode(userId, code);
        } else if (userId) {
            result = await UserPersistedDataBusiness.GetByUserId(userId);
        } else {
            result = await UserPersistedDataBusiness.GetAll();
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - CREATE - //
// ---------- //

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const data:UserPersistedDataModel = await request.json();
        await UserPersistedDataBusiness.Create(data);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - UPDATE - //
// ---------- //

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const data:UserPersistedDataModel = await request.json();
        await UserPersistedDataBusiness.Update(data);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - DELETE - //
// ---------- //

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const code = params.get("code");
        const userId = params.get("userId");

        if ((!id || !code) && !userId) {
            return NextResponse.json({ error: "Missing 'id' and 'code', or 'userId' parameter" }, { status: 400 });
        }

        if (id && code) {
            await UserPersistedDataBusiness.Delete(id, code);
        } else if (userId) {
            await UserPersistedDataBusiness.DeleteByUserId(userId);
        }

        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});