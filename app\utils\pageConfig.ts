// Page configuration utility

/**
 * Utility class for page configuration in N-Grave
 */
export class JC_Utils_PageConfig {
    /**
     * List of paths that should have header and footer hidden
     */
    static pagesWithHiddenHeaderFooter = [
        '/admin/products',
        '/admin/ingredients',
        '/admin/printSettings',
        '/admin/printFrontLabel',
        '/admin/printBackLabel',
        '/demo/menu',
        '/demo/products',
        '/demo/ingredients',
        '/demo/printSettings',
        '/demo/printFrontLabel',
        '/demo/printBackLabel',
        '/loginRegister',
    ];

    /**
     * List of paths that don't require authentication
     */
    static publicPaths = [
        '/loginRegister',
        '/forgotPassword',
        '/resetPassword',
        '/privacyPolicy',
        // Only specific API routes are public, handled by JC_Utils_Auth
    ];

    /**
     * Check if the current path should have header and footer hidden
     * @param path Current page path
     * @returns Boolean indicating if header and footer should be hidden
     */
    static shouldHideHeaderFooter(path: string): boolean {
        // If path is empty, return false (don't hide header/footer by default)
        if (!path) return false;

        // Check if the path exactly matches or starts with any of the paths in the list
        return this.pagesWithHiddenHeaderFooter.some(hiddenPath =>
            path === hiddenPath ||
            path.startsWith(`${hiddenPath}/`)
        );
    }

    /**
     * Check if the current path is public (doesn't require authentication)
     * @param path Current page path
     * @returns Boolean indicating if the path is public
     */
    static isPublicPath(path: string): boolean {
        // If path is empty, return false (require authentication by default)
        if (!path) return false;

        // Check if the path exactly matches or starts with any of the paths in the list
        return this.publicPaths.some(publicPath =>
            path === publicPath ||
            path.startsWith(`${publicPath}/`)
        );
    }
}