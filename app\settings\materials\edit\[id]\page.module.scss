@import '../../../../global';

.mainContainer {
    @include editPageMainContainerStyles;

    .pageWrapper {
        width: auto;
        display: flex;
        flex-direction: column;
    }

    .threeDotsMenuContainer {
        position: relative;
    }

    .headerContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .formContainer {
        width: 100%;
        margin-top: 20px;
    }

    .priceFieldsRow {
        display: flex;
        flex-direction: row;
        column-gap: 30px;
        align-items: flex-start;
        width: 100%;
    }

    .methodsContainer {
        margin-top: 10px;
        width: 100%;

        .methodsLabel {
            display: block;
            font-weight: bold;
            color: $white;
            text-align: center;
        }

        .methodsList {
            display: flex;
            flex-direction: column;
            gap: 0;
            overflow-y: auto;
            padding: 10px;
            background-color: rgba($offBlack, 0.5);

            .methodItem {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 8px;
                border-radius: $tinyBorderRadius;
                cursor: pointer;
                transition: background-color 0.2s ease;
                color: $white;
                user-select: none;

                &:hover {
                    background-color: rgba($primaryColor, 0.1);
                }
            }
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .headerContainer {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
    }
}
