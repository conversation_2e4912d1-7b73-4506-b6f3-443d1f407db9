import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import bcrypt from "bcryptjs";

import { UserModel } from "@/app/models/User";
import { UserBusiness } from "./business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

// Get by email
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {

    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userEmail = params.get("userEmail")!;
        const result = await UserBusiness.GetByEmail(userEmail);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});

// Create
export async function PUT(request: NextRequest) {
    console.log('User registration API called');

    try {
        console.log('Parsing request data');
        const requestData:{ userData:UserModel, password:string } = await request.json();
        const userData = requestData.userData;
        const password = requestData.password;

        console.log('Checking if email exists:', userData.Email);
        // Check if email already exists
        if ((await UserBusiness.GetByEmail(userData.Email)) != null) {
            console.log('Email already exists');
            return NextResponse.json({ error: "This email already exists!" }, { status: 500 });
        }

        console.log('Hashing password');
        userData.PasswordHash = await bcrypt.hash(password, 12);

        console.log('Creating user');
        await UserBusiness.Create(userData);
        console.log('User created successfully');
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.error('Error in user registration:', error);
        return NextResponse.json({ error }, { status: 500 });
    }

}

// Update
export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {



        const userData:UserModel = await request.json();

        await JC_Utils_Business.sqlUpdate(UserModel, userData);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});