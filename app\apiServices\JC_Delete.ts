import { JC_Utils } from "../Utils";
import { _ModelConstructor } from "../models/_ModelRequirements";

export async function JC_Delete<T>(mapper: _ModelConstructor<T>, routeName:string, id:string) : Promise<boolean> {
    const response = await fetch(`/api/${routeName}?${new URLSearchParams({id})}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
    });
    if (!response.ok) { throw new Error(`Failed to delete ${JC_Utils.routeNameToDescription(routeName)} with Id "${id}".`); };

    // Clear cache for this table after successful delete operation
    // Create a model instance to access ExtendedFields for clearing referenced model caches
    const modelInstance = new mapper();
    JC_Utils.clearLocalStorageForTable(mapper.tableName, modelInstance);

    return true;
}