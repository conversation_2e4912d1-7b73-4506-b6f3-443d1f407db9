SELECT "method"."Id"
      ,"method"."Name"
      ,"method"."CostPrice"
      ,"method"."PercentMarkup"
      ,"method"."SalesPrice"
      ,"method"."IsSalesPriceReadOnlyOn"
      ,"method"."CostPerSecond"
      ,"method"."CreatedAt"
      ,"method"."ModifiedAt"
      ,"method"."Deleted"
FROM public."Method" "method"
WHERE 1=1
      AND "method"."Deleted" = 'False'
ORDER BY "method"."Name";

-- Delete
-- UPDATE public."Method"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."Method"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
