import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ClientModel } from "@/app/models/Client";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all Clients
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const result = await JC_Utils_Business.sqlGetList(ClientModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            Sorts: [{ SortField: "Name", SortAsc: true }]
        });
        return NextResponse.json({ result: result.ResultList }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
