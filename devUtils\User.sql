SELECT "user"."Id"
      ,"user"."FirstName"
      ,"user"."LastName"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "FullName"
      ,"user"."Email"
      ,"user"."PasswordHash"
      ,"user"."LoginFailedAttempts"
      ,"user"."LoginLockoutDate"
      ,"user"."ChangePasswordToken"
      ,"user"."ChangePasswordTokenDate"
      ,"user"."Phone"
      ,"user"."IsAdmin"
      ,"user"."IsWholesale"
      ,"user"."CompanyName"
      ,"user"."IsEmailSubscribed"
      ,"user"."IsDiscountUser"
      ,"user"."StripeCustomerId"
      ,"user"."IsVerified"
      ,"user"."VerificationToken"
      ,"user"."CreatedAt"
      ,"user"."ModifiedAt"
      ,"user"."Deleted"
FROM public."User" "user"
WHERE 1=1
      AND "user"."Deleted" = 'False'
ORDER BY "user"."LastName", "user"."FirstName";

-- Set as Admin
UPDATE public."User"
SET "IsAdmin"    = 'True',
-- SET "IsAdmin"    = 'False',
    "ModifiedAt" = NOW()
WHERE "Id" = '5e9c2b6f-b2a8-4b3c-84a9-51c2cd0dccd7'

-- Delete
-- UPDATE public."User"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."User"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
