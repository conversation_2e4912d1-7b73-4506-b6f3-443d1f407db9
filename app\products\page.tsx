"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_List from "../components/JC_List/JC_List";
import JC_Button from "../components/JC_Button/JC_Button";
import JC_Tooltip from "../components/JC_Tooltip/JC_Tooltip";
import { TooltipPositionEnum } from "../enums/TooltipPosition";
import { ClientModel } from "../models/Client";
import { MaterialModel } from "../models/Material";
import { MethodModel } from "../models/Method";
import { ProductModel } from "../models/Product";
import { JC_Utils } from "../Utils";

export default function Page_Products() {
    const router = useRouter();
    const [clients, setClients] = useState<ClientModel[]>([]);
    const [materials, setMaterials] = useState<MaterialModel[]>([]);
    const [methods, setMethods] = useState<MethodModel[]>([]);
    const [initialised, setInitialised] = useState<boolean>(false);

    // Load reference data on page load
    useEffect(() => {
        async function loadReferenceData() {
            try {
                // Load clients
                const clientsResult = await ClientModel.GetList();
                setClients(clientsResult.ResultList);

                // Load materials
                const materialsResult = await MaterialModel.GetList();
                setMaterials(materialsResult.ResultList);

                // Load methods
                const methodsResult = await MethodModel.GetList();
                setMethods(methodsResult.ResultList);

                setInitialised(true);
            } catch (error) {
                console.error("Error loading reference data:", error);
                JC_Utils.showToastError("Failed to load reference data.");
                setInitialised(true);
            }
        }

        loadReferenceData();
    }, []);



    // Handle new product
    function handleNewProduct() {
        // Check if methods exist
        if (methods.length === 0) {
            JC_Utils.showToastWarning("You must create a Method first!");
            return;
        }
        router.push("/products/edit/new");
    }

    // Get client name by ID
    const getClientName = (clientId: string): string => {
        if (!clients) return "Unknown Client";
        const client = clients.find(c => c.Id === clientId);
        return client ? client.Name : "Unknown Client";
    };

    // Get material name by ID
    const getMaterialName = (materialId: string): string => {
        const material = materials.find(m => m.Id === materialId);
        return material ? material.Name : "Unknown Material";
    };

    // Get material details for tooltip
    const getMaterialDetails = (materialId: string): MaterialModel | undefined => {
        return materials.find(m => m.Id === materialId);
    };

    // Get method name by ID
    const getMethodName = (product: ProductModel): string => {
        // If Ex_MethodName is populated, use it
        if (product.Ex_MethodName) {
            return product.Ex_MethodName;
        }

        // Fallback to finding method by ID
        const method = methods.find(m => m.Id === product.MethodId);
        return method ? method.Name : 'Unknown Method';
    };

    // Get method details for tooltip
    const getMethodDetails = (methodId: string): MethodModel | undefined => {
        return methods.find(m => m.Id === methodId);
    };

    // Handle edit product
    const handleEditProduct = (id: string) => {
        router.push(`/products/edit/${id}`);
    };

    // Generate headers for products list
    const getProductsListHeaders = () => {
        return [
            { label: "Client", sortKey: "ClientId" },
            { label: "Name", sortKey: "Name" },
            { label: "Material", sortKey: "MaterialId", hideOnTiny: true },
            { label: "Method", sortKey: "MethodId", hideOnTiny: true },
            { label: "Width", sortKey: "DimensionX", hideOnMedium: true },
            { label: "Height", sortKey: "DimensionY", hideOnMedium: true },
            { label: "Area", sortKey: "Ex_Area", hideOnLarge: true },
            { label: "Profit Margin", sortKey: "ProfitMargin", hideOnSmall: true },
            { label: "Sale", sortKey: "SalesPrice", hideOnTeenyTiny: true },
            { label: "Created At", sortKey: "CreatedAt", hideOnMedium: true }
        ];
    };

    // Render row for products list
    const renderProductRow = (product: ProductModel) => {
        return (
            <tr
                key={product.Id}
                onClick={() => handleEditProduct(product.Id)}
            >
                <td>{getClientName(product.ClientId)}</td>
                <td>{product.Name}</td>
                <td className={styles.materialCell}>
                    {(() => {
                        const materialName = getMaterialName(product.MaterialId);
                        const material = getMaterialDetails(product.MaterialId);

                        if (!material) return materialName;

                        return (
                            <>
                                {materialName}
                                <JC_Tooltip
                                    content={
                                        <div className={styles.tooltipContent}>
                                            ${material.CostPrice.toFixed(2)}
                                            <span className={styles.separator}>|</span>
                                            {material.PercentMarkup.toFixed(2)}%
                                            <span className={styles.separator}>|</span>
                                            ${material.SalesPrice.toFixed(2)}
                                        </div>
                                    }
                                    position={TooltipPositionEnum.Top}
                                    absoluteFillSpace={true}
                                />
                            </>
                        );
                    })()}
                </td>
                <td className={styles.methodCell}>
                    {(() => {
                        const methodName = getMethodName(product);
                        const method = getMethodDetails(product.MethodId);

                        if (!method) return methodName;

                        return (
                            <>
                                {methodName}
                                <JC_Tooltip
                                    content={
                                        <div className={styles.tooltipContent}>
                                            ${method.CostPrice.toFixed(2)}
                                            <span className={styles.separator}>|</span>
                                            {method.PercentMarkup.toFixed(2)}%
                                            <span className={styles.separator}>|</span>
                                            ${method.SalesPrice.toFixed(2)}
                                        </div>
                                    }
                                    position={TooltipPositionEnum.Top}
                                    absoluteFillSpace={true}
                                />
                            </>
                        );
                    })()}
                </td>
                <td>{Math.round(product.DimensionX)}mm</td>
                <td>{Math.round(product.DimensionY)}mm</td>
                <td>{parseFloat(product.Ex_Area.toFixed(0))}<span dangerouslySetInnerHTML={{ __html: "mm<sup>2</sup>" }} /></td>
                <td>{`${product.ProfitMargin.toFixed(2)}%`}</td>
                <td>${product.SalesPrice.toFixed(2)}</td>
                <td>{new Date(product.CreatedAt).toLocaleDateString() + ' ' + new Date(product.CreatedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true}).replace(' ', '')}</td>
            </tr>
        );
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Products", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Products" />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <div style={{ position: 'relative', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <JC_List
                            service={ProductModel.GetList}
                            headers={getProductsListHeaders()}
                            defaultSortKey="Name"
                            defaultSortAsc={true}
                            row={renderProductRow}
                        />
                        <JC_Button
                            text="+"
                            onClick={handleNewProduct}
                            isSecondary
                            isCircular
                            overrideClass={styles.addButton}
                        />
                    </div>
                )}
            </JC_PageContentContainer>
        </div>
    );
}
