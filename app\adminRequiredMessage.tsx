"use client"

import styles from "./adminRequiredMessage.module.scss";
import React, { useState } from "react";
import Image from "next/image";
import { signOut } from "next-auth/react";
import J<PERSON>_Button from "./components/JC_Button/JC_Button";
import JC_PageContentContainer from "./components/JC_PageContentContainer/JC_PageContentContainer";

export default function AdminRequiredMessage() {
    const [logoutLoading, setLogoutLoading] = useState<boolean>(false);

    return (
        <div className={styles.mainContainer}>
            <div className={styles.logoContainer}>
                <Image
                    src="/logos/Main.png"
                    width={380}
                    height={92}
                    alt="N-Grave Logo"
                    className={styles.logo}
                />
            </div>
            <JC_PageContentContainer>
                <h1>Admin Only</h1>
                <p>You must be enabled as Admin<br/>to access this site.</p>
                <JC_Button
                    text="Logout"
                    onClick={() => { setLogoutLoading(true); signOut({ callbackUrl: "/loginRegister" }); }}
                    isLoading={logoutLoading}
                    overrideClass={styles.logoutButton}
                />
            </JC_PageContentContainer>
        </div>
    );
}
