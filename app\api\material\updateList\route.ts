import { NextRequest, NextResponse } from "next/server";
import { MaterialModel } from "@/app/models/Material";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const materials:MaterialModel[] = await request.json();
        for (const material of materials) {
            await JC_Utils_Business.sqlUpdate(MaterialModel, material);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
