"use client"

import styles from "./JC_Footer.module.scss";
import React from "react";
import Link from "next/link";
import { FooterProvider, useFooter } from "./JC_FooterContext";
import { JC_Utils } from "../../Utils";

// Inner component that uses the context
function FooterContent() {
    const { menuOpen, toggleMenu, closeMenu, menuRef, burgerRef } = useFooter();

    // Function to check if we're on a specific page
    const isOnPage = (path: string) => {
        return JC_Utils.isOnPage(path);
    };

    // Function to handle link click based on current page
    const handleLinkClick = (path: string, event: React.MouseEvent) => {
        if (isOnPage(path)) {
            // If we're already on this page, prevent navigation
            event.preventDefault();
        } else {
            // Otherwise, close the menu and allow navigation
            closeMenu();
        }
    };

    return (
        <div className={styles.mainContainer} id="JC_footer">
            {/* Burger Menu Button */}
            <div
                ref={burgerRef}
                className={`${styles.burgerButton} ${menuOpen ? styles.active : ''}`}
                onClick={toggleMenu}
            >
                <div className={styles.burgerIcon}>
                    <span className={`${styles.menuText} ${menuOpen ? styles.hidden : ''}`}>≡</span>
                    <span className={`${styles.closeText} ${menuOpen ? '' : styles.hidden}`}>×</span>
                </div>
            </div>

            {/* Navigation Menu */}
            <div
                ref={menuRef}
                className={`${styles.navMenu} ${menuOpen ? styles.open : ''}`}
            >
                <Link
                    className={`${styles.navLink} ${isOnPage('settings') ? styles.currentPage : ''}`}
                    href="/settings"
                    onClick={(e) => handleLinkClick('settings', e)}
                >
                    Settings
                </Link>
                <Link
                    className={`${styles.navLink} ${isOnPage('clients') ? styles.currentPage : ''}`}
                    href="/clients"
                    onClick={(e) => handleLinkClick('clients', e)}
                >
                    Clients
                </Link>
                <Link
                    className={`${styles.navLink} ${isOnPage('products') ? styles.currentPage : ''}`}
                    href="/products"
                    onClick={(e) => handleLinkClick('products', e)}
                >
                    Products
                </Link>
                <Link
                    className={`${styles.navLink} ${isOnPage('tools') ? styles.currentPage : ''}`}
                    href="/tools"
                    onClick={(e) => handleLinkClick('tools', e)}
                >
                    Tools
                </Link>
            </div>
        </div>
    );
}

// Main component that provides the context
export default function JC_Footer() {
    return (
        <FooterProvider>
            <FooterContent />
        </FooterProvider>
    );
}
