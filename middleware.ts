import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { JC_Utils_Auth } from './app/utils/authUtils';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Clone the response and add the pathname to the headers
  const response = NextResponse.next();
  response.headers.set('x-pathname', pathname);

  // Check if this is an API route
  if (pathname.startsWith('/api')) {
    // Check authentication for API routes
    const authResponse = await JC_Utils_Auth.checkApiAuth(request);
    if (authResponse) {
      return authResponse;
    }
  }

  return response;
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - static assets
     */
    '/((?!_next/static|_next/image|_next/data|favicon.ico|.*\\.png$|.*\\.svg$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.css$|.*\\.js$|.*\\.json$|.*\\.woff$|.*\\.woff2$|.*\\.ttf$|.*\\.otf$).*)',
  ],
};
