"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../../../components/JC_Title/JC_Title";
import <PERSON><PERSON>_<PERSON>ton from "../../../../components/JC_Button/JC_Button";
import JC_Form from "../../../../components/JC_Form/JC_Form";
import J<PERSON>_Field from "../../../../components/JC_Field/JC_Field";
import J<PERSON>_Spinner from "../../../../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../../../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_PageContentContainer from "../../../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_Checkbox from "../../../../components/JC_Checkbox/JC_Checkbox";
import JC_AffectedProductsModal from "../components/JC_AffectedProductsModal";
import JC_ThreeDotsMenu from "../../../../components/JC_ThreeDotsMenu/JC_ThreeDotsMenu";
import JC_ModalConfirmation from "../../../../components/JC_ModalConfirmation/JC_ModalConfirmation";
import { MaterialModel } from "../../../../models/Material";
import { MethodModel } from "../../../../models/Method";
import { ProductModel } from "../../../../models/Product";
import { JC_Get } from "../../../../apiServices/JC_Get";
import { JC_Utils, JC_Utils_Pricing } from "../../../../Utils";
import { FieldTypeEnum } from "../../../../enums/FieldType";
import { JC_ConfirmationModalUsageModel } from "../../../../models/ComponentModels/JC_ConfirmationModalUsage";

export default function Page_EditMaterial({ params }: { params: { id: string } }) {
    const router = useRouter();
    const isNew = params.id === "new";

    const [material, setMaterial] = useState<MaterialModel>(new MaterialModel());
    const [methods, setMethods] = useState<MethodModel[]>([]);
    const [selectedMethodIds, setSelectedMethodIds] = useState<string[]>([]);
    const [originalMethodIds, setOriginalMethodIds] = useState<string[]>([]);
    const [initialised, setInitialised] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>("");

    // Affected products modal states
    const [showAffectedProductsModal, setShowAffectedProductsModal] = useState<boolean>(false);
    const [affectedProducts, setAffectedProducts] = useState<ProductModel[]>([]);
    const [isCheckingProducts, setIsCheckingProducts] = useState<boolean>(false);

    // Confirmation modal states
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>(null);
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);

    // Field states
    const [name, setName] = useState<string>("");
    const [costPrice, setCostPrice] = useState<number>(0);
    const [percentMarkup, setPercentMarkup] = useState<number>(0);
    const [salesPrice, setSalesPrice] = useState<number>(0);
    const [isPercentMarkupReadOnly, setIsPercentMarkupReadOnly] = useState<boolean>(false);
    const [isSalesPriceReadOnly, setIsSalesPriceReadOnly] = useState<boolean>(true);

    // Load material and methods on page load
    useEffect(() => {
        const loadData = async () => {
            try {
                // Load methods first
                const methodsResult = await MethodModel.GetAll();
                setMethods(methodsResult);

                // Check if methods exist when creating a new material
                if (isNew && methodsResult.length === 0) {
                    // Redirect to home page and show warning
                    router.push("/");
                    JC_Utils.showToastWarning("You must create a Method first!");
                    return;
                }

                // If editing existing material, load it
                if (!isNew) {
                    const materialResult = await JC_Get<MaterialModel>(
                        MaterialModel,
                        MaterialModel.apiRoute,
                        { id: params.id }
                    );

                    setMaterial(materialResult);
                    setName(materialResult.Name);
                    setCostPrice(materialResult.CostPrice);

                    // Set field values
                    setPercentMarkup(materialResult.PercentMarkup);
                    setSalesPrice(materialResult.SalesPrice);

                    // Set readonly states based on IsSalesPriceReadOnlyOn flag
                    setIsPercentMarkupReadOnly(materialResult.IsSalesPriceReadOnlyOn);
                    setIsSalesPriceReadOnly(!materialResult.IsSalesPriceReadOnlyOn);

                    // Set selected method IDs
                    try {
                        const methodIds = JSON.parse(materialResult.MethodIdsListJson);
                        setSelectedMethodIds(methodIds);
                        setOriginalMethodIds(methodIds); // Store original method IDs for comparison
                    } catch (error) {
                        console.error("Error parsing method IDs:", error);
                        setSelectedMethodIds([]);
                        setOriginalMethodIds([]);
                    }
                }

                setInitialised(true);
            } catch (error) {
                console.error("Error loading data:", error);
                JC_Utils.showToastError("Failed to load data.");
                setInitialised(true);
            }
        };

        loadData();
    }, [isNew, params.id]);

    // Check for affected products
    const checkAffectedProducts = async () => {
        if (isNew) {
            // No need to check for affected products when creating a new material
            return [];
        }

        // Find methods that have been removed
        const removedMethodIds = originalMethodIds.filter(id => !selectedMethodIds.includes(id));

        if (removedMethodIds.length === 0) {
            // No methods have been removed, so no products are affected
            return [];
        }

        try {
            setIsCheckingProducts(true);

            // Get all products that use this material
            const productsResponse = await ProductModel.GetByMaterialId(material.Id);

            // Filter products that use removed methods
            const affected = productsResponse.ResultList.filter(product =>
                removedMethodIds.includes(product.MethodId)
            );

            return affected;
        } catch (error) {
            console.error("Error checking affected products:", error);
            JC_Utils.showToastError("Failed to check affected products.");
            return [];
        } finally {
            setIsCheckingProducts(false);
        }
    };

    // Handle saving material after updating affected products
    const saveMaterial = async () => {
        try {
            // Update material object with form values
            material.Name = name;
            material.CostPrice = costPrice;
            material.MethodIdsListJson = JSON.stringify(selectedMethodIds);

            // Set both fields with their current values
            material.PercentMarkup = percentMarkup;
            material.SalesPrice = salesPrice;

            // Set the IsSalesPriceReadOnlyOn flag based on which field is readonly
            material.IsSalesPriceReadOnlyOn = isPercentMarkupReadOnly;

            // Create or update material
            if (isNew) {
                await MaterialModel.Create(material);
                JC_Utils.showToastSuccess("Material created successfully.");
            } else {
                await MaterialModel.Update(material);
                JC_Utils.showToastSuccess("Material updated successfully.");
            }

            // Navigate back to materials list
            router.push("/settings/materials");
        } catch (error) {
            console.error("Error saving material:", error);
            setErrorMessage("Failed to save material.");
            setIsLoading(false);
        }
    };

    // Handle updating affected products and saving material
    const handleUpdateProductsAndSave = async (updatedProducts: ProductModel[]) => {
        try {
            setIsLoading(true);

            // Update each affected product with its new method
            if (updatedProducts.length > 0) {
                await ProductModel.UpdateList(updatedProducts);
            }

            // Save the material
            await saveMaterial();
        } catch (error) {
            console.error("Error updating products:", error);
            setErrorMessage("Failed to update affected products.");
            setIsLoading(false);
            setShowAffectedProductsModal(false);
        }
    };

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);
            setErrorMessage("");

            // Check for affected products
            const affected = await checkAffectedProducts();

            if (affected.length > 0) {
                // Show modal with affected products
                setAffectedProducts(affected);
                setShowAffectedProductsModal(true);
                setIsLoading(false);
            } else {
                // No affected products, proceed with saving
                await saveMaterial();
            }
        } catch (error) {
            console.error("Error during submission:", error);
            setErrorMessage("Failed to process submission.");
            setIsLoading(false);
        }
    };

    // Toggle between percent markup and sales price
    const togglePriceField = (isPercentMarkupField: boolean) => {
        // Only toggle if the clicked field is readonly
        if (isPercentMarkupField && isPercentMarkupReadOnly) {
            // Calculate percent markup from current sales price before switching
            if (costPrice > 0) {
                // Calculate and set the percent markup based on the current sales price
                const calculatedPercentMarkup = JC_Utils_Pricing.calculatePercentMarkup(costPrice, salesPrice);
                setPercentMarkup(calculatedPercentMarkup);
            }

            // Switch to percent markup editable
            setIsPercentMarkupReadOnly(false);
            setIsSalesPriceReadOnly(true);
        } else if (!isPercentMarkupField && isSalesPriceReadOnly) {
            // Calculate sales price from current percent markup before switching
            // Calculate and set the sales price based on the current percent markup
            const calculatedSalesPrice = JC_Utils_Pricing.calculateSalesPrice(costPrice, percentMarkup);
            setSalesPrice(calculatedSalesPrice);

            // Switch to sales price editable
            setIsPercentMarkupReadOnly(true);
            setIsSalesPriceReadOnly(false);
        }
    };

    // Handle cost price change
    const handleCostPriceChange = (value: string) => {
        const newCostPrice = parseFloat(value) || 0;
        setCostPrice(newCostPrice);

        // Update the non-readonly field based on the new cost price
        if (isPercentMarkupReadOnly) {
            // Recalculate percent markup
            setPercentMarkup(JC_Utils_Pricing.calculatePercentMarkup(newCostPrice, salesPrice));
        } else if (isSalesPriceReadOnly) {
            // Recalculate sales price
            setSalesPrice(JC_Utils_Pricing.calculateSalesPrice(newCostPrice, percentMarkup));
        }
    };

    // Handle percent markup change
    const handlePercentMarkupChange = (value: string) => {
        const newPercentMarkup = parseFloat(value) || 0;
        setPercentMarkup(newPercentMarkup);

        // Update sales price based on new percent markup
        setSalesPrice(JC_Utils_Pricing.calculateSalesPrice(costPrice, newPercentMarkup));
    };

    // Handle sales price change
    const handleSalesPriceChange = (value: string) => {
        const newSalesPrice = parseFloat(value) || 0;
        setSalesPrice(newSalesPrice);

        // Update percent markup based on new sales price
        if (costPrice > 0) {
            setPercentMarkup(JC_Utils_Pricing.calculatePercentMarkup(costPrice, newSalesPrice));
        }
    };

    // Toggle method selection
    const toggleMethodSelection = (methodId: string) => {
        if (selectedMethodIds.includes(methodId)) {
            setSelectedMethodIds(selectedMethodIds.filter(id => id !== methodId));
        } else {
            setSelectedMethodIds([...selectedMethodIds, methodId]);
        }
    };

    // Handle delete material
    const handleDeleteMaterial = async () => {
        if (isNew) return; // Don't show delete option for new materials

        try {
            // Check if this material is used in any products
            const productsUsingMaterial = await ProductModel.GetByMaterialId(material.Id);

            if (productsUsingMaterial.ResultList.length > 0) {
                // Show warning that material is in use
                setConfirmationModalData({
                    title: "Cannot Delete Material",
                    text: `This material is used in ${productsUsingMaterial.ResultList.length} product(s). Please remove it from all products before deleting.`,
                    submitButtons: []
                });
                return;
            }

            // If not in use, show delete confirmation
            setConfirmationModalData({
                title: "Delete Material",
                text: `Are you sure you want to delete "${name}"?`,
                submitButtons: [{
                    text: "Delete",
                    onSubmit: async () => {
                        try {
                            setConfirmationLoading(true);
                            await MaterialModel.Delete(params.id);
                            setConfirmationLoading(false);
                            setConfirmationModalData(null);
                            JC_Utils.showToastSuccess("Material deleted successfully.");
                            router.push("/settings/materials");
                        } catch (error) {
                            console.error("Error deleting material:", error);
                            setConfirmationLoading(false);
                            JC_Utils.showToastError("Failed to delete material.");
                        }
                    }
                }]
            });
        } catch (error) {
            console.error("Error checking product usage:", error);
            JC_Utils.showToastError("Failed to check if material is in use.");
        }
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Settings", path: "/settings" },
                    { label: "Materials", path: "/settings/materials" },
                    { label: isNew ? "New Material" : "Edit Material", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title={isNew ? "New Material" : "Edit Material"} />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <div className={styles.formContainer}>
                        <JC_Form
                            submitButtonText="Save"
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                {
                                    inputId: "name",
                                    type: FieldTypeEnum.Text,
                                    label: "Name",
                                    value: name,
                                    onChange: (value) => setName(value),
                                    validate: (value) =>
                                        JC_Utils.stringNullOrEmpty(value as string)
                                            ? "Name is required"
                                            : ""
                                },
                                {
                                    inputId: "price-fields",
                                    type: FieldTypeEnum.Custom,
                                    customNode: (
                                        <div className={styles.priceFieldsRow}>
                                            <JC_Field
                                                inputId="costPrice"
                                                type={FieldTypeEnum.Number}
                                                label="Cost ($/m²)"
                                                value={costPrice}
                                                onChange={handleCostPriceChange}
                                                decimalPlaces={2}
                                            />
                                            <JC_Field
                                                inputId="percentMarkup"
                                                type={FieldTypeEnum.Number}
                                                label="Markup (%)"
                                                value={percentMarkup}
                                                onChange={handlePercentMarkupChange}
                                                decimalPlaces={2}
                                                readOnly={isPercentMarkupReadOnly}
                                                onClick={() => togglePriceField(true)}
                                                inputOverrideClass={isPercentMarkupReadOnly ? styles.readOnlyToggle : ""}
                                            />
                                            <JC_Field
                                                inputId="salesPrice"
                                                type={FieldTypeEnum.Number}
                                                label="Sale ($)"
                                                value={salesPrice}
                                                onChange={handleSalesPriceChange}
                                                decimalPlaces={2}
                                                readOnly={isSalesPriceReadOnly}
                                                onClick={() => togglePriceField(false)}
                                                inputOverrideClass={isSalesPriceReadOnly ? styles.readOnlyToggle : ""}
                                            />
                                        </div>
                                    )
                                },
                                {
                                    inputId: "methods",
                                    type: FieldTypeEnum.Custom,
                                    label: "Methods",
                                    value: "",
                                    validate: () =>
                                        selectedMethodIds.length === 0
                                            ? "Select at least one method"
                                            : "",
                                    readOnly: true,
                                    customNode: (
                                        <div className={styles.methodsContainer}>
                                            <label className={styles.methodsLabel}>Methods</label>
                                            <div className={styles.methodsList}>
                                                {methods.map(method => (
                                                    <div
                                                        key={method.Id}
                                                        className={styles.methodItem}
                                                        onClick={() => toggleMethodSelection(method.Id)}
                                                    >
                                                        <JC_Checkbox
                                                            checked={selectedMethodIds.includes(method.Id)}
                                                            onChange={() => toggleMethodSelection(method.Id)}
                                                        />
                                                        <span>{method.Name}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )
                                }
                            ]}
                        />
                    </div>
                )}
            </JC_PageContentContainer>

            {/* Affected Products Modal */}
            <JC_AffectedProductsModal
                isOpen={showAffectedProductsModal}
                onCancel={() => setShowAffectedProductsModal(false)}
                onSave={handleUpdateProductsAndSave}
                affectedProducts={affectedProducts}
                availableMethods={methods.filter(method => selectedMethodIds.includes(method.Id))}
                isLoading={isLoading}
            />

            {/* Confirmation Modal */}
            {confirmationModalData && (
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />
            )}
        </div>
    );
}
