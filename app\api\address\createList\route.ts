import { NextRequest, NextResponse } from "next/server";
import { AddressModel } from "@/app/models/Address";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const addresses:AddressModel[] = await request.json();
        for (const address of addresses) {
            await JC_Utils_Business.sqlCreate(AddressModel, address);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
