import { sql } from "@vercel/postgres";
import { MaterialModel } from "../../models/Material";

export class MaterialBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id:string) {
        // Get the material with method names using a join
        const result = await sql`
            WITH material AS (
                SELECT "Id",
                       "Name",
                       "MethodIdsListJson",
                       "CostPrice",
                       "PercentMarkup",
                       "SalesPrice",
                       "IsSalesPriceReadOnlyOn",
                       "CreatedAt",
                       "ModifiedAt",
                       "Deleted"
                FROM public."Material"
                WHERE "Id" = ${id}
                  AND "Deleted" = 'False'
            ),
            method_ids AS (
                SELECT jsonb_array_elements_text(m."MethodIdsListJson"::jsonb) AS method_id
                FROM material m
            ),
            method_names AS (
                SELECT mth."Name"
                FROM method_ids mi
                JOIN public."Method" mth ON mth."Id" = mi.method_id::uuid
                WHERE mth."Deleted" = 'False'
            )
            SELECT m.*,
                   COALESCE(array_agg(mn."Name") FILTER (WHERE mn."Name" IS NOT NULL), '{}') AS "Ex_MethodNames",
                   COALESCE(jsonb_array_length(m."MethodIdsListJson"::jsonb), 0) AS "Ex_MethodsCount"
            FROM material m
            LEFT JOIN method_names mn ON true
            GROUP BY m."Id", m."Name", m."MethodIdsListJson", m."CostPrice", m."PercentMarkup",
                     m."SalesPrice", m."IsSalesPriceReadOnlyOn", m."CreatedAt", m."ModifiedAt", m."Deleted"
        `;

        // Convert the result to MaterialModel
        const material = result.rows[0];

        return material;
    }

    static async GetAll() {
        // Get all materials with method names using a join
        const result = await sql`
            WITH materials AS (
                SELECT "Id",
                       "Name",
                       "MethodIdsListJson",
                       "CostPrice",
                       "PercentMarkup",
                       "SalesPrice",
                       "IsSalesPriceReadOnlyOn",
                       "CreatedAt",
                       "ModifiedAt",
                       "Deleted"
                FROM public."Material"
                WHERE "Deleted" = 'False'
            ),
            material_method_ids AS (
                SELECT m."Id" AS material_id,
                       jsonb_array_elements_text(m."MethodIdsListJson"::jsonb) AS method_id
                FROM materials m
            ),
            material_method_names AS (
                SELECT mmi.material_id,
                       mth."Name" AS method_name
                FROM material_method_ids mmi
                JOIN public."Method" mth ON mth."Id" = mmi.method_id::uuid
                WHERE mth."Deleted" = 'False'
            )
            SELECT m.*,
                   COALESCE(array_agg(mmn.method_name) FILTER (WHERE mmn.method_name IS NOT NULL), '{}') AS "Ex_MethodNames",
                   COALESCE(jsonb_array_length(m."MethodIdsListJson"::jsonb), 0) AS "Ex_MethodsCount"
            FROM materials m
            LEFT JOIN material_method_names mmn ON m."Id" = mmn.material_id
            GROUP BY m."Id", m."Name", m."MethodIdsListJson", m."CostPrice", m."PercentMarkup",
                     m."SalesPrice", m."IsSalesPriceReadOnlyOn", m."CreatedAt", m."ModifiedAt", m."Deleted"
            ORDER BY m."Name"
        `;

        // Convert the result to MaterialModel array
        const materials = result.rows;

        return materials;
    }

    // - ------ - //
    // - CREATE - //

}
