
## Startup

* Create+clone repo
* Copy everything in JCTemplateWeb (except "." and node-modules folders) into repo
* Push
* In "#Startup.txt" fill out first section in Environment and the Code Replacements section 
* Vercel
    * Setup project on selected account, connect the repo (need to add repo to permissions)
        * Will probably fail when it first tries to deploy, just go to "Deployments" and re-deploy.
    * Setup db
        * Name format "casella-web-postgres"
        * Click "Copy Snippet"
        * Paste over db variables in "#Startup.txt"
        * Add connection to Azure Data Studio
* IF need Resend
    * Setup new account using client's email
    * Add client's custom domain
    * Add dns records to domain provider account and verify
    * Add API Key to "#Startup.txt"
* IF need Stripe
    * Create Stripe account
    * Add keys+secret to "#Startup.txt"
* IF need Google Maps, add site's domiain to Google Maps API
* Setup dbml specific for project
* Describe pages at bottom of "#Startup.txt"
* Push
* Run the "#Startup.txt" in Augment
    * May need to run "continue" if stops since it will run a lot of things.
* Double check changes in Sourcetree
* Add any .env.local variables to Vercel env variables
* Run "dbScripts/Init.sql" on it
* Run, login, navigate every page to make sure doesn't fail
    * IF fails, make fix, then add fix to JCTemplateWeb, then re-do this process
* Push
* Remove any unused folders
* Push
* Add favicon and any required images in "public", re-run to check
* Delete this file
* Push
* Begin building other pages and adjusting existing pages