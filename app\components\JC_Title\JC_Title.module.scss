@import '../../global';

.mainContainer {
    margin-top: -16px;
    width: 100%;
    height: max-content !important;
    text-align: center;
    font-size: 56px;
    font-family: var(--font-k2d);
    letter-spacing: 5px;
    color: $primaryColor;
    user-select: none;

    &.secondary {
        color: $secondaryColor;
    }
}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        font-size: 42px !important;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        font-size: 36px !important;
    }
}