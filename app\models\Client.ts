import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class ClientModel extends _Base implements _ModelRequirements {

    static tableName: string = "Client";
    static apiRoute: string = "client";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    static cacheMinutes_get = 10080; // 1 week
    static cacheMinutes_getList = 10080; // 1 week

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static async Get(id: string) {
        return await JC_Get<ClientModel>(ClientModel, ClientModel.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${ClientModel.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?: JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<ClientModel>(ClientModel, `${ClientModel.apiRoute}/getList`, paging, undefined, abortSignal);
    }
    static async Create(data: ClientModel) {
        return await JC_Put<ClientModel>(ClientModel, ClientModel.apiRoute, data);
    }
    static async CreateList(dataList: ClientModel[]) {
        return await JC_PutRaw<ClientModel[]>(`${ClientModel.apiRoute}/createList`, dataList, undefined, "Client");
    }
    static async Update(data: ClientModel) {
        return await JC_Post<ClientModel>(ClientModel, ClientModel.apiRoute, data);
    }
    static async UpdateList(dataList: ClientModel[]) {
        return await JC_PostRaw<ClientModel[]>(`${ClientModel.apiRoute}/updateList`, dataList, undefined, "Client");
    }
    static async Delete(id: string) {
        return await JC_Delete(ClientModel, ClientModel.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${ClientModel.apiRoute}/deleteList`, { ids }, undefined, "Client");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;

    // Extended
    Ex_ProductsCount: number;
    Ex_ProductNames: string[];

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        {
            Name: "Ex_ProductsCount",
            FromField: "Id",
            ReferenceModel: ClientModel,
            setWithCallback: async (instance: ClientModel) => {
                // Count products for this client
                const { sql } = await import("@vercel/postgres");
                const result = await sql`
                    SELECT COUNT(*) as count
                    FROM public."Product"
                    WHERE "ClientId" = ${instance.Id}
                      AND "Deleted" = 'False'
                `;
                return parseInt(result.rows[0].count);
            }
        },
        {
            Name: "Ex_ProductNames",
            FromField: "Id",
            ReferenceModel: ClientModel,
            setWithCallback: async (instance: ClientModel) => {
                // Get product names for this client (limit 5, add "..." if more)
                const { sql } = await import("@vercel/postgres");
                const countResult = await sql`
                    SELECT COUNT(*) as count
                    FROM public."Product"
                    WHERE "ClientId" = ${instance.Id}
                      AND "Deleted" = 'False'
                `;
                const totalCount = parseInt(countResult.rows[0].count);

                const namesResult = await sql`
                    SELECT "Name"
                    FROM public."Product"
                    WHERE "ClientId" = ${instance.Id}
                      AND "Deleted" = 'False'
                    ORDER BY "Name"
                    LIMIT 5
                `;

                const names = namesResult.rows.map(row => row.Name);

                if (totalCount > 5) {
                    names.push("...");
                }

                return names;
            }
        }
    ];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ClientModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        // Extended
        this.Ex_ProductsCount = 0;
        this.Ex_ProductNames = [];

        Object.assign(this, init);

        // Ensure number fields are actually numbers
        this.Ex_ProductsCount = Number(this.Ex_ProductsCount);
    }

    static getKeys() {
        return Object.keys(new ClientModel());
    }

    static jcFieldTypeforField(fieldName: keyof ClientModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "Name":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}
