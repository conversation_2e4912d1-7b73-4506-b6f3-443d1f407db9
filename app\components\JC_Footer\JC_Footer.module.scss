@import '../../global';

.mainContainer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-color: transparent;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    // Burger Menu Button
    .burgerButton {
        position: fixed;
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: $primaryColor;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 101;
        user-select: none;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.1);
        }

        .burgerIcon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            position: relative;

            .menuText, .closeText {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 28px;
                color: $offBlack;
                transition: opacity 0.3s ease;
                line-height: 1;
                text-align: center;
            }

            .menuText {
                margin-top: 1px;
            }
            .closeText {
                margin-top: -1px;
            }

            .hidden {
                opacity: 0;
            }
        }
    }

    // Navigation Menu
    .navMenu {
        position: fixed;
        bottom: 92px; // Position above the burger button
        left: 20px; // Align with the burger button
        width: max-content;
        background-color: $offBlack;
        border: 2px solid $primaryColor;
        border-radius: $smallBorderRadius;
        display: flex;
        flex-direction: column; // Vertical layout
        align-items: center; // Center the links horizontally
        padding: 8px 10px;
        transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
        z-index: 99; // Below burger button
        @include containerShadow;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        overflow: hidden;

        &.open {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            transition: opacity 0.3s ease, visibility 0s linear 0s, transform 0.3s ease;
        }

        .navLink {
            color: $primaryColor;
            padding: 10px 25px;
            margin: 2px 0;
            font-size: 16px;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s ease;
            white-space: nowrap;
            width: 100%;
            border-radius: $tinyBorderRadius;
            box-sizing: border-box;
            position: relative;
            user-select: none;

            &:hover {
                color: lighten($primaryColor, 10%);
                background-color: rgba($primaryColor, 0.1);
            }

            &.currentPage {
                opacity: 0.5;
                cursor: default;

                &:hover {
                    background-color: transparent;
                }
            }
        }
    }


}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .burgerButton {
            width: 45px;
            height: 45px;

            .burgerLine {
                width: 22px;
            }
        }

        .navMenu {
            bottom: 75px;

            .navLink {
                font-size: 14px;
                padding: 8px 12px;
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        .burgerButton {
            width: 40px;
            height: 40px;
            bottom: 15px;
            left: 15px;

            .burgerIcon {
                .menuText, .closeText {
                    font-size: 24px;
                }
            }
        }

        .navMenu {
            bottom: 65px;
            padding: 8px;

            .navLink {
                font-size: 12px;
                padding: 6px 10px;
            }
        }

    }
}