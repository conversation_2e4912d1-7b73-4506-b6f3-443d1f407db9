import styles from "./JC_CasellaWeb.module.scss";
import React from 'react';
import Image from "next/image";
import Link from 'next/link';
import { JC_Utils } from "@/app/Utils";

export default function JC_CasellaWeb(_: Readonly<{
    overrideClass?: string;
}>) {
    return (
        <Link
            className={`${styles.mainContainer} ${_.overrideClass || ''}`}
            href="https://casellaweb.com.au/"
            target="_blank"
            rel="noopener noreferrer"
        >
            <div className={styles.text}>Created by</div>
            <Image
                className={styles.logo}
                src="/CasellaWebLogo.png"
                width={40}
                height={40}
                alt="CasellaWeb"
                unoptimized
            />
        </Link>
    );
}
