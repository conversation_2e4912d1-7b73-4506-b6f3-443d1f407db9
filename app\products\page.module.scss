@import '../global';

.mainContainer {
    @include listPageMainContainerStyles;

    .headerContainer {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
    }

    .addButton {
        margin-top: 30px;
        div {
            margin-top: -3px;
        }
    }
}

.materialCell, .methodCell {
    position: relative;
    cursor: pointer;
}

.tooltipContent {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 12px 16px;
    border-radius: $tinyBorderRadius;
    background-color: rgba($offBlack, 0.5);
    color: $white;
    text-align: center;

    .separator {
        margin: 0 8px;
        opacity: 0.6;
        color: $primaryColor;
        font-weight: bold;
    }
}