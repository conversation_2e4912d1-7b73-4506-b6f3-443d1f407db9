import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { FieldTypeEnum } from "../enums/FieldType";

export class GlobalSettingsModel extends _Base implements _ModelRequirements {

    static tableName: string = "GlobalSettings";
    static apiRoute: string = "globalSettings";
    static primaryKey: string = "Code";
    static primaryDisplayField: string = "Description";

    static cacheMinutes_get = 10080; // 1 week
    static cacheMinutes_getList = 10080; // 1 week

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Description: string;
    Value: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<GlobalSettingsModel>) {
        super();
        this.Code = "";
        this.Description = "";
        this.Value = "";
        Object.assign(this, init);
    }

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        // Add extended field definitions here if needed
    ];

    static getKeys() {
        return Object.keys(new GlobalSettingsModel());
    }

    static jcFieldTypeforField(fieldName: keyof GlobalSettingsModel) {
        switch (fieldName) {
            case "Code":
                return FieldTypeEnum.Text;
            case "Description":
                return FieldTypeEnum.Text;
            case "Value":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Description} | ${this.Value}`;
    }
}
