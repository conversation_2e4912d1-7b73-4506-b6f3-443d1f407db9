SELECT "address"."Id"
      ,"address"."UserId"
      ,"user"."FirstName" + ' ' + "user"."LastName" "__User"
      ,"address"."Line1"
      ,"address"."Line2"
      ,"address"."City"
      ,"address"."Postcode"
      ,"address"."Country"
      ,"address"."CreatedAt"
      ,"address"."ModifiedAt"
      ,"address"."Deleted"
FROM public."Address" "address"
INNER JOIN public."User" "user" ON "address"."UserId" = "user"."Id"
WHERE 1=1
      AND "address"."Deleted" = 'False'
ORDER BY "user"."LastName", "user"."FirstName";

-- Delete
-- UPDATE public."Address"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."Address"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
