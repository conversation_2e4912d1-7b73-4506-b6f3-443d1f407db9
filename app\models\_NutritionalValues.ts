export class _NutritionalValuesModel {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Kilojoules: number;
    Protein: number;
    FatTotal: number;
    FatSaturated: number;
    Carbohydrate: number;
    Sugars: number;
    Fiber: number;
    Sodium: number;
    PercAus: number;
    CostPer100g: number;

    // Extended
    Ex_NetCarbs: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<_NutritionalValuesModel>) {
        this.Kilojoules = 0;
        this.Protein = 0;
        this.FatTotal = 0;
        this.FatSaturated = 0;
        this.Carbohydrate = 0;
        this.Sugars = 0;
        this.Fiber = 0;
        this.Sodium = 0;
        this.PercAus = 0;
        this.CostPer100g = 0;
        Object.assign(this, init);

        // Ensure number fields are actually numbers
        this.Kilojoules = Number(this.Kilojoules);
        this.Protein = Number(this.Protein);
        this.FatTotal = Number(this.FatTotal);
        this.FatSaturated = Number(this.FatSaturated);
        this.Carbohydrate = Number(this.Carbohydrate);
        this.Sugars = Number(this.Sugars);
        this.Fiber = Number(this.Fiber);
        this.Sodium = Number(this.Sodium);
        this.PercAus = Number(this.PercAus);
        this.CostPer100g = Number(this.CostPer100g);

        // Extended
        // Get NetCarbs based on Carbohydrate and Fiber
        this.Ex_NetCarbs = this.Carbohydrate - this.Fiber;
        // In case there was error in incoming values, make sure set min PercAus
        this.PercAus = Math.min(100, this.PercAus);
    }
}
