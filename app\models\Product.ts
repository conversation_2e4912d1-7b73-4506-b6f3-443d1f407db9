import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel, JC_ListPagingResultModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";
import { ClientModel } from "./Client";
import { MethodModel } from "./Method";

export class ProductModel extends _Base implements _ModelRequirements {

    static tableName: string = "Product";
    static apiRoute: string = "product";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    static cacheMinutes_get = 10080; // 1 week
    static cacheMinutes_getList = 10080; // 1 week

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static async Get(id: string) {
        return await JC_Get<ProductModel>(ProductModel, ProductModel.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${ProductModel.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?: JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<ProductModel>(ProductModel, `${ProductModel.apiRoute}/getList`, paging, undefined, abortSignal);
    }

    // Helper method to create a paging model with filters
    static createPagingWithFilters(filters: {
        clientId?: string;
        materialId?: string;
        methodId?: string;
        width?: number;
        height?: number;
    }, basePaging?: JC_ListPagingModel): JC_ListPagingModel {
        const conditions: string[] = [];

        if (filters.clientId && filters.clientId.trim() !== '') {
            // Clean the UUID value to remove any trailing characters
            const cleanClientId = filters.clientId.trim().replace(/[?&].*$/, '');
            conditions.push(`main."ClientId" = '${cleanClientId}'`);
        }

        if (filters.materialId && filters.materialId.trim() !== '') {
            // Clean the UUID value to remove any trailing characters
            const cleanMaterialId = filters.materialId.trim().replace(/[?&].*$/, '');
            conditions.push(`main."MaterialId" = '${cleanMaterialId}'`);
        }

        if (filters.methodId && filters.methodId.trim() !== '') {
            // Clean the UUID value to remove any trailing characters
            const cleanMethodId = filters.methodId.trim().replace(/[?&].*$/, '');
            conditions.push(`main."MethodId" = '${cleanMethodId}'`);
        }

        if (filters.width !== undefined && filters.width > 0) {
            conditions.push(`main."DimensionX" = ${Number(filters.width)}`);
        }

        if (filters.height !== undefined && filters.height > 0) {
            conditions.push(`main."DimensionY" = ${Number(filters.height)}`);
        }

        return {
            ...basePaging,
            Filters: conditions.length > 0 ? conditions : undefined
        };
    }
    static async GetByMaterialId(materialId: string) {
        return await JC_GetRaw<JC_ListPagingResultModel<ProductModel>>(`${ProductModel.apiRoute}/byMaterial`, { materialId });
    }
    static async Create(data: ProductModel) {
        return await JC_Put<ProductModel>(ProductModel, ProductModel.apiRoute, data);
    }
    static async CreateList(dataList: ProductModel[]) {
        return await JC_PutRaw<ProductModel[]>(`${ProductModel.apiRoute}/createList`, dataList, undefined, "Product");
    }
    static async Update(data: ProductModel) {
        return await JC_Post<ProductModel>(ProductModel, ProductModel.apiRoute, data);
    }
    static async UpdateList(dataList: ProductModel[]) {
        return await JC_PostRaw<ProductModel[]>(`${ProductModel.apiRoute}/updateList`, dataList, undefined, "Product");
    }
    static async Delete(id: string) {
        return await JC_Delete(ProductModel, ProductModel.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${ProductModel.apiRoute}/deleteList`, { ids }, undefined, "Product");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ClientId: string;
    Name: string;
    MaterialId: string;
    MethodId: string;
    DimensionX: number;
    DimensionY: number;
    ProfitMargin: number;
    SalesPrice: number;
    IsSalesPriceReadOnlyOn: boolean;

    // Extended
    Ex_MethodName: string;
    Ex_ClientName: string;
    Ex_Area: number;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        { Name: "Ex_MethodName", FromField: "MethodId", ReferenceModel: MethodModel },
        { Name: "Ex_ClientName", FromField: "ClientId", ReferenceModel: ClientModel }
    ];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ProductModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.ClientId = "";
        this.Name = "";
        this.MaterialId = "";
        this.MethodId = "";
        this.DimensionX = 0;
        this.DimensionY = 0;
        this.ProfitMargin = 0;
        this.SalesPrice = 0;
        this.IsSalesPriceReadOnlyOn = false;
        // Extended
        this.Ex_MethodName = "";
        this.Ex_ClientName = "";
        this.Ex_Area = 0;

        Object.assign(this, init);

        // Ensure number fields are actually numbers
        this.DimensionX = Number(this.DimensionX);
        this.DimensionY = Number(this.DimensionY);
        this.ProfitMargin = Number(this.ProfitMargin);
        this.SalesPrice = Number(this.SalesPrice);
        this.IsSalesPriceReadOnlyOn = Boolean(this.IsSalesPriceReadOnlyOn);
        this.Ex_Area = Number(this.Ex_Area);
    }

    static getKeys() {
        return Object.keys(new ProductModel());
    }

    static jcFieldTypeforField(fieldName: keyof ProductModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "ClientId":
                return FieldTypeEnum.Dropdown;
            case "Name":
                return FieldTypeEnum.Text;
            case "MaterialId":
                return FieldTypeEnum.Dropdown;
            case "MethodId":
                return FieldTypeEnum.Dropdown;
            case "DimensionX":
                return FieldTypeEnum.Number;
            case "DimensionY":
                return FieldTypeEnum.Number;
            case "ProfitMargin":
                return FieldTypeEnum.Number;
            case "SalesPrice":
                return FieldTypeEnum.Number;
            case "IsSalesPriceReadOnlyOn":
                return FieldTypeEnum.Custom;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `Product ${this.Id}`;
    }
}
