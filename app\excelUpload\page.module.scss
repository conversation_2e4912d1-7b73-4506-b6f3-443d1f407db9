@import '../global';

.mainContainer {
    @include fullWidthMainContainerStyles;

    .headerContainer {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .dropZoneContainer {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 30px;
        
        .dropZone {
            width: 80%;
            min-height: 300px;
            border: 3px dashed $primaryColor;
            border-radius: $largeBorderRadius;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            
            &.dragActive {
                background-color: rgba($primaryColor, 0.1);
                border-color: $secondaryColor;
            }
            
            .dropZoneText {
                color: $secondaryColor;
                font-size: 24px;
                text-align: center;
                margin-bottom: 15px;
            }
            
            .dropZoneSubtext {
                color: $secondaryColor;
                font-size: 16px;
                text-align: center;
                opacity: 0.7;
            }
        }
    }
    
    .tableContainer {
        width: 100%;
        margin-top: 20px;
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .dropZoneContainer {
            .dropZone {
                width: 90%;
                min-height: 250px;
                padding: 20px;
                
                .dropZoneText {
                    font-size: 20px;
                }
                
                .dropZoneSubtext {
                    font-size: 14px;
                }
            }
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        .dropZoneContainer {
            .dropZone {
                width: 95%;
                min-height: 200px;
                padding: 15px;
                
                .dropZoneText {
                    font-size: 18px;
                }
                
                .dropZoneSubtext {
                    font-size: 12px;
                }
            }
        }
    }
}
