-- Drop tables in reverse order of creation to handle dependencies
-- This script will drop all tables in the database

-- First, drop tables with foreign key dependencies (Level 2)
DROP TABLE IF EXISTS public."Product" CASCADE;
DROP TABLE IF EXISTS public."Address" CASCADE;
DROP TABLE IF EXISTS public."UserPersistedData" CASCADE;

-- Next, drop intermediate tables (Level 1)
DROP TABLE IF EXISTS public."Material" CASCADE;
DROP TABLE IF EXISTS public."Method" CASCADE;
DROP TABLE IF EXISTS public."Client" CASCADE;

-- Finally, drop independent tables (Level 0)
DROP TABLE IF EXISTS public."User" CASCADE;
DROP TABLE IF EXISTS public."GlobalSettings" CASCADE;
DROP TABLE IF EXISTS public."OrderStatus" CASCADE;

-- Confirm all tables have been dropped
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE';

    RAISE NOTICE 'Remaining tables in public schema: %', table_count;
END $$;