import { NextRequest, NextResponse } from 'next/server';
import { EmailBusiness } from '../business';

export async function POST(request: NextRequest) {
    console.log('Welcome email API called');

    try {
        console.log('Parsing request data');
        const { name, email } = await request.json();
        console.log('Sending welcome email to:', email);
        await EmailBusiness.SendWelcomeEmail(name, email);
        console.log('Welcome email sent successfully');
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.error('Error sending welcome email:', error);
        return NextResponse.json({ error }, { status: 500 });
    }

}