"use client"

import styles from "./page.module.scss";
import Link from "next/link";
import { useState } from "react";
import { useFooter } from "../components/JC_Footer/JC_FooterContext";
import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_ModalConfirmation from "../components/JC_ModalConfirmation/JC_ModalConfirmation";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";
import { JC_Utils } from "../Utils";
import { JC_ConfirmationModalUsageModel } from "../models/ComponentModels/JC_ConfirmationModalUsage";

export default function Page_Settings() {
    const { closeMenu } = useFooter();

    // Confirmation modal states
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>(null);
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);

    const handleClearCache = () => {
        setConfirmationModalData({
            title: "Clear Cache",
            text: "Are you sure you want to clear cache?",
            submitButtons: [{
                text: "Clear Cache",
                onSubmit: async () => {
                    try {
                        setConfirmationLoading(true);
                        JC_Utils.clearAllLocalStorage();
                        setConfirmationLoading(false);
                        setConfirmationModalData(null);
                        JC_Utils.showToastSuccess("Cache cleared successfully!");
                    } catch (error) {
                        console.error("Error clearing cache:", error);
                        setConfirmationLoading(false);
                        JC_Utils.showToastError("Failed to clear cache.");
                    }
                }
            }]
        });
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Settings", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={false}>
                <div className={styles.tilesGrid}>
                    <Link href="/settings/methods" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Methods</div>
                    </Link>
                    <Link href="/settings/materials" className={styles.tile} onClick={closeMenu}>
                        <div className={styles.tileContent}>Materials</div>
                    </Link>
                    <div className={styles.tile} onClick={handleClearCache}>
                        <div className={styles.tileContent}>Clear Cache</div>
                    </div>
                </div>
            </JC_PageContentContainer>

            {/* Confirmation Modal */}
            {confirmationModalData && (
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />
            )}
        </div>
    );
}
