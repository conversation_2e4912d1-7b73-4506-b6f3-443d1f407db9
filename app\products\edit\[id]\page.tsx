"use client";

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../../components/JC_Title/JC_Title";
import JC_Form from "../../../components/JC_Form/JC_Form";
import J<PERSON>_Field from "../../../components/JC_Field/JC_Field";
import J<PERSON>_Spinner from "../../../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_PageContentContainer from "../../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_ThreeDotsMenu from "../../../components/JC_ThreeDotsMenu/JC_ThreeDotsMenu";
import JC_ModalConfirmation from "@/app/components/JC_ModalConfirmation/JC_ModalConfirmation";
import J<PERSON>_Modal from "../../../components/JC_Modal/JC_Modal";
import <PERSON><PERSON>_Button from "../../../components/JC_Button/JC_Button";
import JC_Checkbox from "../../../components/JC_Checkbox/JC_Checkbox";
import JC_List from "../../../components/JC_List/JC_List";
import JC_Tooltip from "../../../components/JC_Tooltip/JC_Tooltip";
import {
  TooltipPositionEnum,
  CheckboxLabelPositionEnum,
} from "../../../enums/TooltipPosition";
import { ProductModel } from "../../../models/Product";
import { ClientModel } from "../../../models/Client";
import { MaterialModel } from "../../../models/Material";
import { MethodModel } from "../../../models/Method";
import { JC_Get } from "../../../apiServices/JC_Get";
import { JC_Utils, JC_Utils_Pricing } from "../../../Utils";
import { FieldTypeEnum } from "../../../enums/FieldType";
import { JC_FieldOption } from "@/app/models/ComponentModels/JC_FieldOption";
import { JC_ConfirmationModalUsageModel } from "@/app/models/ComponentModels/JC_ConfirmationModalUsage";
import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";

export default function Page_EditProduct({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const isNew = params.id === "new";

  // Function to load filter checkbox values from localStorage
  const loadFilterCheckboxesFromLocalStorage = () => {
    const client = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterClient
    );
    const name = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterName
    );
    const material = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterMaterial
    );
    const method = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterMethod
    );
    const width = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterWidth
    );
    const height = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterHeight
    );
    const profitMargin = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterProfitMargin
    );
    const finalSale = JC_Utils.safeLocalStorageGetItem(
      LocalStorageKeyEnum.JC_ProductsFormFilterFinalSale
    );

    return {
      client: client !== null ? client === "true" : true, // Default to true if not set
      name: name !== null ? name === "true" : true,
      material: material !== null ? material === "true" : true,
      method: method !== null ? method === "true" : true,
      width: width !== null ? width === "true" : true,
      height: height !== null ? height === "true" : true,
      profitMargin: profitMargin !== null ? profitMargin === "true" : false,
      finalSale: finalSale !== null ? finalSale === "true" : true,
    };
  };

  // Get clientId from URL if provided (for creating a new product from client page)
  const getClientIdFromUrl = (): string | null => {
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get("clientId");
    }
    return null;
  };

  const [product, setProduct] = useState<ProductModel>(new ProductModel());
  const [clients, setClients] = useState<ClientModel[]>([]);
  const [materials, setMaterials] = useState<MaterialModel[]>([]);
  const [methods, setMethods] = useState<MethodModel[]>([]);
  const [filteredMethods, setFilteredMethods] = useState<MethodModel[]>([]);
  const [initialised, setInitialised] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Products list state
  const [filteredProducts, setFilteredProducts] = useState<ProductModel[]>([]);
  const [productsListLoading, setProductsListLoading] =
    useState<boolean>(false);

  // Field states
  const [clientId, setClientId] = useState<string>("");
  const [name, setName] = useState<string>("");
  const [materialId, setMaterialId] = useState<string>("");
  const [methodId, setMethodId] = useState<string>("");
  const [dimensionX, setDimensionX] = useState<number>(0);
  const [dimensionY, setDimensionY] = useState<number>(0);
  const [profitMargin, setProfitMargin] = useState<number>(0);
  const [salesPrice, setSalesPrice] = useState<number>(0);
  const [genCostPrice, setGenCostPrice] = useState<number>(0);
  const [getSalePrice, setGetSalePrice] = useState<number>(0);
  const [isProfitMarginReadOnly, setIsProfitMarginReadOnly] =
    useState<boolean>(false);
  const [isFinalSalesPriceReadOnly, setIsFinalSalesPriceReadOnly] =
    useState<boolean>(true);

  // Confirmation modal states
  const [confirmationModalData, setConfirmationModalData] =
    useState<JC_ConfirmationModalUsageModel | null>(null);
  const [confirmationLoading, setConfirmationLoading] =
    useState<boolean>(false);

  // Auto-fill modal states
  const [isAutoFillModalOpen, setIsAutoFillModalOpen] =
    useState<boolean>(false);
  const [selectedProductForAutoFill, setSelectedProductForAutoFill] =
    useState<ProductModel | null>(null);
  const [autoFillCheckboxes, setAutoFillCheckboxes] = useState({
    client: true,
    name: true,
    material: true,
    method: true,
    width: true,
    height: true,
    profitMargin: false,
    finalSale: true,
  });

  // Filter checkboxes state - indicates which fields are included in filtering
  const [filterCheckboxes, setFilterCheckboxes] = useState(
    loadFilterCheckboxesFromLocalStorage()
  );

  // Ensure material has both PercentMarkup and SalesPrice values
  const calculateMaterialNullValues = (
    material: MaterialModel
  ): MaterialModel => {
    const updatedMaterial = { ...material };

    // If IsSalesPriceReadOnlyOn is true, recalculate PercentMarkup from SalesPrice
    if (updatedMaterial.IsSalesPriceReadOnlyOn) {
      updatedMaterial.PercentMarkup = JC_Utils_Pricing.calculatePercentMarkup(
        updatedMaterial.CostPrice,
        updatedMaterial.SalesPrice
      );
    } else {
      // Otherwise, recalculate SalesPrice from PercentMarkup
      updatedMaterial.SalesPrice = JC_Utils_Pricing.calculateSalesPrice(
        updatedMaterial.CostPrice,
        updatedMaterial.PercentMarkup
      );
    }

    return updatedMaterial;
  };

  // Ensure method has both PercentMarkup and SalesPrice values
  const calculateMethodNullValues = (method: MethodModel): MethodModel => {
    const updatedMethod = { ...method };

    // If IsSalesPriceReadOnlyOn is true, recalculate PercentMarkup from SalesPrice
    if (updatedMethod.IsSalesPriceReadOnlyOn) {
      updatedMethod.PercentMarkup = JC_Utils_Pricing.calculatePercentMarkup(
        updatedMethod.CostPrice,
        updatedMethod.SalesPrice
      );
    } else {
      // Otherwise, recalculate SalesPrice from PercentMarkup
      updatedMethod.SalesPrice = JC_Utils_Pricing.calculateSalesPrice(
        updatedMethod.CostPrice,
        updatedMethod.PercentMarkup
      );
    }

    return updatedMethod;
  };

  // Load product and reference data on page load
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load clients
        const clientsResult = await ClientModel.GetList();
        setClients(clientsResult.ResultList);

        // Load materials
        const materialsResult = await MaterialModel.GetList();

        // Calculate null values for each material
        const processedMaterials = materialsResult.ResultList.map((material) =>
          calculateMaterialNullValues(material)
        );
        setMaterials(processedMaterials);

        // Load methods
        const methodsResult = await MethodModel.GetList();

        // Calculate null values for each method
        const processedMethods = methodsResult.ResultList.map((method) =>
          calculateMethodNullValues(method)
        );
        setMethods(processedMethods);

        // Check if methods exist when creating a new product
        if (isNew && methodsResult.ResultList.length === 0) {
          // Redirect to home page and show warning
          router.push("/");
          JC_Utils.showToastWarning("You must create a Method first!");
          return;
        }

        // If editing existing product, load it
        if (!isNew) {
          const productResult = await JC_Get<ProductModel>(
            ProductModel,
            ProductModel.apiRoute,
            { id: params.id }
          );

          setProduct(productResult);
          setClientId(productResult.ClientId);
          setName(productResult.Name || "");
          setMaterialId(productResult.MaterialId);
          setMethodId(productResult.MethodId);
          setDimensionX(productResult.DimensionX);
          setDimensionY(productResult.DimensionY);
          setProfitMargin(productResult.ProfitMargin);
          setSalesPrice(productResult.SalesPrice);

          // Filter methods based on selected material
          filterMethodsByMaterial(productResult.MaterialId);

          // Calculate cost price
          const calculatedCost = calculateTotalCost();
          setGenCostPrice(calculatedCost);

          // Calculate sale price
          const calculatedSale = calculateTotalSale();
          setGetSalePrice(calculatedSale);

          // Set field values
          setProfitMargin(productResult.ProfitMargin);
          setSalesPrice(productResult.SalesPrice);

          // Set readonly states based on IsSalesPriceReadOnlyOn flag
          setIsProfitMarginReadOnly(productResult.IsSalesPriceReadOnlyOn);
          setIsFinalSalesPriceReadOnly(!productResult.IsSalesPriceReadOnlyOn);
        } else {
          // For new products, check if clientId is provided in URL
          const urlClientId = getClientIdFromUrl();
          if (urlClientId) {
            // Find the client in the loaded clients
            const client = clientsResult.ResultList.find(
              (c) => c.Id === urlClientId
            );
            if (client) {
              setClientId(urlClientId);
              // Set default values for a new product
              setIsProfitMarginReadOnly(false);
              setIsFinalSalesPriceReadOnly(true);
            }
          }
        }

        setInitialised(true);
      } catch (error) {
        console.error("Error loading data:", error);
        JC_Utils.showToastError("Failed to load data.");
        setInitialised(true);
      }
    };

    loadData();
  }, [isNew, params.id]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setErrorMessage("");

      // Update product object with form values
      product.ClientId = clientId;
      product.Name = name;
      product.MaterialId = materialId;
      product.MethodId = methodId;
      product.DimensionX = dimensionX;
      product.DimensionY = dimensionY;

      // Set both fields with their current values
      product.ProfitMargin = profitMargin;
      product.SalesPrice = salesPrice;

      // Set the IsSalesPriceReadOnlyOn flag based on which field is readonly
      product.IsSalesPriceReadOnlyOn = isProfitMarginReadOnly;

      // Create or update product
      if (isNew) {
        await ProductModel.Create(product);
        JC_Utils.showToastSuccess("Product created successfully.");
      } else {
        await ProductModel.Update(product);
        JC_Utils.showToastSuccess("Product updated successfully.");
      }

      // Check if we came from a client page
      const urlClientId = getClientIdFromUrl();
      if (isNew && urlClientId) {
        // Navigate back to the client edit page
        router.push(`/clients/edit/${urlClientId}`);
      } else {
        // Navigate back to products list
        router.push("/products");
      }
    } catch (error) {
      console.error("Error saving product:", error);
      setErrorMessage("Failed to save product.");
      setIsLoading(false);
    }
  };

  // Get selected material details
  const getSelectedMaterial = (): MaterialModel | undefined => {
    return materials.find((m) => m.Id === materialId);
  };

  // Get selected method details
  const getSelectedMethod = (): MethodModel | undefined => {
    return methods.find((m) => m.Id === methodId);
  };

  // Filter methods based on selected material
  const filterMethodsByMaterial = (materialId: string) => {
    if (!materialId || methods.length === 0) {
      // If no material is selected or methods aren't loaded, show no methods
      setFilteredMethods([]);
      return;
    }

    const selectedMaterial = materials.find((m) => m.Id === materialId);
    if (!selectedMaterial) {
      setFilteredMethods([]);
      return;
    }

    try {
      // Parse the method IDs from the material's MethodIdsListJson
      const methodIds = JSON.parse(selectedMaterial.MethodIdsListJson);

      // Filter methods to only include those in the material's method list
      const availableMethods = methods.filter((method) =>
        methodIds.includes(method.Id)
      );

      setFilteredMethods(availableMethods);

      // If current method is not in the filtered list, reset it
      if (methodId && !methodIds.includes(methodId)) {
        setMethodId("");
      }

      // Log for debugging
      console.log(
        `Filtered methods for material ${selectedMaterial.Name}:`,
        availableMethods.map((m) => m.Name).join(", ")
      );
    } catch (error) {
      console.error("Error parsing method IDs:", error);
      setFilteredMethods([]);
    }
  };

  // Calculate total cost based on material, method, and dimensions
  const calculateTotalCost = (): number => {
    const material = getSelectedMaterial();
    const method = getSelectedMethod();

    if (!material || !method || dimensionX <= 0 || dimensionY <= 0) {
      return 0;
    }

    // Convert dimensions from mm to m² (divide by 1000 for mm to m, then multiply)
    const areaSqMeters = (dimensionX / 1000) * (dimensionY / 1000);

    // Calculate total cost (material cost + method cost) * area
    const totalCost = (material.CostPrice + method.CostPrice) * areaSqMeters;

    return parseFloat(totalCost.toFixed(2));
  };

  // Calculate total sale price based on material, method, and dimensions
  const calculateTotalSale = (): number => {
    const material = getSelectedMaterial();
    const method = getSelectedMethod();

    if (!material || !method || dimensionX <= 0 || dimensionY <= 0) {
      return 0;
    }

    // Convert dimensions from mm to m² (divide by 1000 for mm to m, then multiply)
    const areaSqMeters = (dimensionX / 1000) * (dimensionY / 1000);

    // Ensure sales prices are calculated if null
    const materialSalesPrice =
      material.SalesPrice ||
      JC_Utils_Pricing.calculateSalesPrice(
        material.CostPrice,
        material.PercentMarkup || 0
      );

    const methodSalesPrice =
      method.SalesPrice ||
      JC_Utils_Pricing.calculateSalesPrice(
        method.CostPrice,
        method.PercentMarkup || 0
      );

    // Calculate total sale price (material sale + method sale) * area
    const totalSale = (materialSalesPrice + methodSalesPrice) * areaSqMeters;

    return parseFloat(totalSale.toFixed(2));
  };

  // Handle navigate to material edit page
  const handleNavigateToMaterial = () => {
    const material = getSelectedMaterial();
    if (!material) return;

    setConfirmationModalData({
      title: "Navigate to Material",
      text: `Are you sure you want to go to the "${material.Name}" edit page?`,
      submitButtons: [
        {
          text: "Go to Material",
          onSubmit: () => {
            setConfirmationLoading(true);
            router.push(`/materials/edit/${material.Id}`);
          },
        },
      ],
    });
  };

  // Handle navigate to method edit page
  const handleNavigateToMethod = () => {
    const method = getSelectedMethod();
    if (!method) return;

    setConfirmationModalData({
      title: "Navigate to Method",
      text: `Are you sure you want to go to the "${method.Name}" edit page?`,
      submitButtons: [
        {
          text: "Go to Method",
          onSubmit: () => {
            setConfirmationLoading(true);
            router.push(`/settings/methods/edit/${method.Id}`);
          },
        },
      ],
    });
  };

  // Toggle between profit margin and final sales price
  const togglePriceField = (isProfitMarginField: boolean) => {
    // Only toggle if the clicked field is readonly
    if (isProfitMarginField && isProfitMarginReadOnly) {
      // Switch to profit margin editable
      setIsProfitMarginReadOnly(false);
      setIsFinalSalesPriceReadOnly(true);

      // Calculate sales price from profit margin
      const totalCost = calculateTotalCost();
      if (totalCost > 0) {
        setSalesPrice(
          JC_Utils_Pricing.calculateSalesPriceFromMargin(
            totalCost,
            profitMargin
          )
        );
      }
    } else if (!isProfitMarginField && isFinalSalesPriceReadOnly) {
      // Switch to final sales price editable
      setIsProfitMarginReadOnly(true);
      setIsFinalSalesPriceReadOnly(false);

      // Calculate profit margin from sales price
      const totalCost = calculateTotalCost();
      if (totalCost > 0 && salesPrice > 0) {
        setProfitMargin(
          JC_Utils_Pricing.calculateProfitMargin(totalCost, salesPrice)
        );
      }
    }
  };

  // Handle profit margin change
  const handleProfitMarginChange = (value: string) => {
    const newProfitMargin = parseFloat(value) || 0;
    setProfitMargin(newProfitMargin);

    // Update sales price based on new profit margin
    const totalCost = calculateTotalCost();
    if (totalCost > 0) {
      setSalesPrice(
        JC_Utils_Pricing.calculateSalesPriceFromMargin(
          totalCost,
          newProfitMargin
        )
      );
    }
  };

  // Handle sales price change
  const handleSalesPriceChange = (value: string) => {
    const newSalesPrice = parseFloat(value) || 0;
    setSalesPrice(newSalesPrice);

    // Update profit margin based on new sales price
    const totalCost = calculateTotalCost();
    if (totalCost > 0) {
      setProfitMargin(
        JC_Utils_Pricing.calculateProfitMargin(totalCost, newSalesPrice)
      );
    }
  };

  // Handle delete product
  const handleDeleteProduct = () => {
    if (isNew) return; // Don't show delete option for new products

    setConfirmationModalData({
      title: "Delete Product",
      text: `Are you sure you want to delete "${name}"?`,
      submitButtons: [
        {
          text: "Delete",
          onSubmit: async () => {
            try {
              setConfirmationLoading(true);
              await ProductModel.Delete(params.id);
              setConfirmationLoading(false);
              setConfirmationModalData(null);
              JC_Utils.showToastSuccess("Product deleted successfully.");

              // Check if we came from a client page
              const urlClientId = getClientIdFromUrl();
              if (urlClientId) {
                // Navigate back to the client edit page
                router.push(`/clients/edit/${urlClientId}`);
              } else {
                // Navigate back to products list
                router.push("/products");
              }
            } catch (error) {
              console.error("Error deleting product:", error);
              setConfirmationLoading(false);
              JC_Utils.showToastError("Failed to delete product.");
            }
          },
        },
      ],
    });
  };

  // Get client name by ID
  const getClientName = (clientId: string): string => {
    if (!clients) return "Unknown Client";
    const client = clients.find((c) => c.Id === clientId);
    return client ? client.Name : "Unknown Client";
  };

  // Get material name by ID
  const getMaterialName = (materialId: string): string => {
    const material = materials.find((m) => m.Id === materialId);
    return material ? material.Name : "Unknown Material";
  };

  // Get material details for tooltip
  const getMaterialDetails = (
    materialId: string
  ): MaterialModel | undefined => {
    return materials.find((m) => m.Id === materialId);
  };

  // Get method name by ID
  const getMethodName = (product: ProductModel): string => {
    // If Ex_MethodName is populated, use it
    if (product.Ex_MethodName) {
      return product.Ex_MethodName;
    }

    // Fallback to finding method by ID
    const method = methods.find((m) => m.Id === product.MethodId);
    return method ? method.Name : "Unknown Method";
  };

  // Get method details for tooltip
  const getMethodDetails = (methodId: string): MethodModel | undefined => {
    return methods.find((m) => m.Id === methodId);
  };

  // Handle edit product
  const handleEditProduct = (id: string) => {
    router.push(`/products/edit/${id}`);
  };

  // Handle product row click for auto-fill
  const handleProductRowClick = (product: ProductModel) => {
    setSelectedProductForAutoFill(product);

    // Set initial checkbox states based on current form state
    const initialCheckboxes = {
      client: true,
      name: true,
      material: true,
      method: true,
      width: true,
      height: true,
      profitMargin: !isProfitMarginReadOnly, // Ticked if profit margin is editable
      finalSale: isProfitMarginReadOnly, // Ticked if profit margin is read-only (final sale is editable)
    };

    setAutoFillCheckboxes(initialCheckboxes);
    setIsAutoFillModalOpen(true);
  };

  // Handle auto-fill checkbox change
  const handleAutoFillCheckboxChange = (
    field: keyof typeof autoFillCheckboxes
  ) => {
    setAutoFillCheckboxes((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Handle filter checkbox change
  const handleFilterCheckboxChange = (field: keyof typeof filterCheckboxes) => {
    const newValue = !filterCheckboxes[field];

    setFilterCheckboxes((prev) => ({
      ...prev,
      [field]: newValue,
    }));

    // Save to localStorage
    const storageKey = {
      client: LocalStorageKeyEnum.JC_ProductsFormFilterClient,
      name: LocalStorageKeyEnum.JC_ProductsFormFilterName,
      material: LocalStorageKeyEnum.JC_ProductsFormFilterMaterial,
      method: LocalStorageKeyEnum.JC_ProductsFormFilterMethod,
      width: LocalStorageKeyEnum.JC_ProductsFormFilterWidth,
      height: LocalStorageKeyEnum.JC_ProductsFormFilterHeight,
      profitMargin: LocalStorageKeyEnum.JC_ProductsFormFilterProfitMargin,
      finalSale: LocalStorageKeyEnum.JC_ProductsFormFilterFinalSale,
    }[field];

    if (storageKey) {
      JC_Utils.safeLocalStorageSetItem(storageKey, newValue.toString());
    }
  };

  // Handle auto-fill confirmation
  const handleAutoFillConfirm = () => {
    if (!selectedProductForAutoFill) return;

    const product = selectedProductForAutoFill;

    // Auto-fill selected fields
    if (autoFillCheckboxes.client) {
      setClientId(product.ClientId);
    }
    if (autoFillCheckboxes.name) {
      setName(product.Name);
    }
    if (autoFillCheckboxes.material) {
      setMaterialId(product.MaterialId);
      // Also filter methods when material changes
      filterMethodsByMaterial(product.MaterialId);
    }
    if (autoFillCheckboxes.method) {
      setMethodId(product.MethodId);
    }
    if (autoFillCheckboxes.width) {
      setDimensionX(product.DimensionX);
    }
    if (autoFillCheckboxes.height) {
      setDimensionY(product.DimensionY);
    }
    if (autoFillCheckboxes.profitMargin) {
      setProfitMargin(product.ProfitMargin);
    }
    if (autoFillCheckboxes.finalSale) {
      setSalesPrice(product.SalesPrice);
    }

    // Close modal
    setIsAutoFillModalOpen(false);
    setSelectedProductForAutoFill(null);
  };

  // Handle auto-fill cancel
  const handleAutoFillCancel = () => {
    setIsAutoFillModalOpen(false);
    setSelectedProductForAutoFill(null);
  };

  // Generate headers for products list
  const getProductsListHeaders = () => {
    return [
      { label: "Client", sortKey: "ClientId" },
      { label: "Name", sortKey: "Name" },
      { label: "Material", sortKey: "MaterialId", hideOnTiny: true },
      { label: "Method", sortKey: "MethodId", hideOnTiny: true },
      { label: "Width", sortKey: "DimensionX", hideOnMedium: true },
      { label: "Height", sortKey: "DimensionY", hideOnMedium: true },
      { label: "Area", sortKey: "Ex_Area", hideOnLarge: true },
      { label: "Profit Margin", sortKey: "ProfitMargin", hideOnSmall: true },
      { label: "Sale", sortKey: "SalesPrice", hideOnTeenyTiny: true },
      { label: "Created At", sortKey: "CreatedAt", hideOnMedium: true },
    ];
  };

  // Render row for products list
  const renderProductRow = (product: ProductModel) => {
    return (
      <tr key={product.Id} onClick={() => handleEditProduct(product.Id)}>
        <td>{getClientName(product.ClientId)}</td>
        <td>{product.Name}</td>
        <td className={styles.materialCell}>
          {(() => {
            const materialName = getMaterialName(product.MaterialId);
            const material = getMaterialDetails(product.MaterialId);

            if (!material) return materialName;

            return (
              <>
                {materialName}
                <JC_Tooltip
                  content={
                    <div className={styles.tooltipContent}>
                      ${material.CostPrice.toFixed(2)}
                      <span className={styles.separator}>|</span>
                      {material.PercentMarkup.toFixed(2)}%
                      <span className={styles.separator}>|</span>$
                      {material.SalesPrice.toFixed(2)}
                    </div>
                  }
                  position={TooltipPositionEnum.Top}
                  absoluteFillSpace={true}
                />
              </>
            );
          })()}
        </td>
        <td className={styles.methodCell}>
          {(() => {
            const methodName = getMethodName(product);
            const method = getMethodDetails(product.MethodId);

            if (!method) return methodName;

            return (
              <>
                {methodName}
                <JC_Tooltip
                  content={
                    <div className={styles.tooltipContent}>
                      ${method.CostPrice.toFixed(2)}
                      <span className={styles.separator}>|</span>
                      {method.PercentMarkup.toFixed(2)}%
                      <span className={styles.separator}>|</span>$
                      {method.SalesPrice.toFixed(2)}
                    </div>
                  }
                  position={TooltipPositionEnum.Top}
                  absoluteFillSpace={true}
                />
              </>
            );
          })()}
        </td>
        <td>{Math.round(product.DimensionX)}mm</td>
        <td>{Math.round(product.DimensionY)}mm</td>
        <td>
          {parseFloat(product.Ex_Area.toFixed(0))}
          <span dangerouslySetInnerHTML={{ __html: "mm<sup>2</sup>" }} />
        </td>
        <td>{`${product.ProfitMargin.toFixed(2)}%`}</td>
        <td>${product.SalesPrice.toFixed(2)}</td>
        <td>
          {new Date(product.CreatedAt).toLocaleDateString() +
            " " +
            new Date(product.CreatedAt)
              .toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
              })
              .replace(" ", "")}
        </td>
      </tr>
    );
  };

  // Load filtered products based on form field values
  const loadFilteredProducts = async () => {
    try {
      setProductsListLoading(true);

      const filters = {
        clientId: filterCheckboxes.client && clientId ? clientId : undefined,
        name: filterCheckboxes.name && name ? name : undefined,
        materialId:
          filterCheckboxes.material && materialId ? materialId : undefined,
        methodId: filterCheckboxes.method && methodId ? methodId : undefined,
        width:
          filterCheckboxes.width && dimensionX > 0 ? dimensionX : undefined,
        height:
          filterCheckboxes.height && dimensionY > 0 ? dimensionY : undefined,
        profitMargin:
          filterCheckboxes.profitMargin && profitMargin > 0
            ? profitMargin
            : undefined,
        finalSale:
          filterCheckboxes.finalSale && salesPrice > 0 ? salesPrice : undefined,
      };

      console.log(
        "Product Edit - loadFilteredProducts called with filters:",
        filters
      );

      // Create paging model with filters
      const paging = ProductModel.createPagingWithFilters(filters);

      console.log("Product Edit - Loading products with paging:", paging);
      const result = await ProductModel.GetList(paging);
      setFilteredProducts(result.ResultList);
    } catch (error) {
      console.error("Error loading filtered products:", error);
      JC_Utils.showToastError("Failed to load products.");
    } finally {
      setProductsListLoading(false);
    }
  };

  // Update filtered methods when methods, materials, or materialId change
  useEffect(() => {
    if (materialId && methods.length > 0) {
      filterMethodsByMaterial(materialId);
    } else {
      // If no material is selected yet or methods aren't loaded, show no methods
      setFilteredMethods([]);
    }
  }, [methods, materials, materialId]);

  // Update cost price and sales price when material, method, or dimensions change
  useEffect(() => {
    const newCostPrice = calculateTotalCost();
    setGenCostPrice(newCostPrice);

    // Calculate the base sales price (from material and method)
    const newSalesPrice = calculateTotalSale();
    setGetSalePrice(newSalesPrice);

    // Update the non-readonly field based on the new cost price
    if (isProfitMarginReadOnly && !isFinalSalesPriceReadOnly) {
      // Recalculate profit margin
      setProfitMargin(
        JC_Utils_Pricing.calculateProfitMargin(newCostPrice, salesPrice)
      );
    } else if (!isProfitMarginReadOnly && isFinalSalesPriceReadOnly) {
      // Recalculate final sales price
      setSalesPrice(
        JC_Utils_Pricing.calculateSalesPriceFromMargin(
          newCostPrice,
          profitMargin
        )
      );
    }
  }, [materialId, methodId, dimensionX, dimensionY]);

  // Load filtered products when form fields or filter checkboxes change
  useEffect(() => {
    if (initialised) {
      loadFilteredProducts();
    }
  }, [
    clientId,
    materialId,
    methodId,
    dimensionX,
    dimensionY,
    filterCheckboxes,
    initialised,
  ]);

  return (
    <div className={styles.mainContainer}>
      <JC_Breadcrumbs
        items={[
          { label: "Home", path: "/" },
          { label: "Products", path: "/products" },
          {
            label: isNew ? "New Product" : "Edit Product",
            path: "",
            isCurrent: true,
          },
        ]}
      />
      <JC_PageContentContainer
        hasBorder={true}
        overrideClass={styles.noBottomMargin}
      >
        {!isNew && initialised && (
          <JC_ThreeDotsMenu
            menuItems={[
              {
                label: "Delete",
                onClick: handleDeleteProduct,
              },
            ]}
            overrideClass={styles.editPageThreeDotsMenu}
          />
        )}
        <div className={styles.headerContainer}>
          <JC_Title title={isNew ? "New Product" : "Edit Product"} />
        </div>

        {!initialised ? (
          <JC_Spinner isPageBody />
        ) : (
          <div className={styles.formContainer}>
            <JC_Form
              submitButtonText="Save"
              onSubmit={handleSubmit}
              isLoading={isLoading}
              errorMessage={errorMessage}
              fields={[
                {
                  inputId: "client",
                  type: FieldTypeEnum.Dropdown,
                  label: "Client",
                  value: clientId,
                  onChange: (value) => {
                    console.log(
                      "Product Edit - Client onChange called with value:",
                      value
                    );
                    setClientId(value);
                  },
                  options: clients.map(
                    (client) =>
                      ({
                        OptionId: client.Id,
                        Label: client.Name,
                      } as JC_FieldOption)
                  ),
                  enableSearch: true,
                  validate: (value) =>
                    JC_Utils.stringNullOrEmpty(value as string)
                      ? "Client is required"
                      : "",
                },
                {
                  inputId: "name",
                  type: FieldTypeEnum.Text,
                  label: "Name",
                  value: name,
                  onChange: (value) => setName(value),
                  validate: (value) =>
                    JC_Utils.stringNullOrEmpty(value as string)
                      ? "Name is required"
                      : "",
                },
                {
                  inputId: "material",
                  type: FieldTypeEnum.Dropdown,
                  label: "Material",
                  value: materialId,
                  onChange: (value) => {
                    console.log(
                      "Product Edit - Material onChange called with value:",
                      value
                    );
                    setMaterialId(value);
                    filterMethodsByMaterial(value);
                  },
                  options: materials.map(
                    (material) =>
                      ({
                        OptionId: material.Id,
                        Label: material.Name,
                      } as JC_FieldOption)
                  ),
                  enableSearch: true,
                  overrideClass: styles.dropdownField,
                  validate: (value) =>
                    JC_Utils.stringNullOrEmpty(value as string)
                      ? "Material is required"
                      : "",
                },
                {
                  inputId: "material-info-container",
                  type: FieldTypeEnum.Custom,
                  customNode: materialId ? (
                    <div
                      className={styles.infoContainer}
                      onClick={handleNavigateToMaterial}
                    >
                      {(() => {
                        const material = getSelectedMaterial();
                        if (!material) return null;
                        return (
                          <>
                            ${material.CostPrice.toFixed(2)}
                            <span className={styles.separator}>|</span>
                            {material.PercentMarkup?.toFixed(2) || "0.00"}%
                            <span className={styles.separator}>|</span>$
                            {material.SalesPrice?.toFixed(2) || "0.00"}
                          </>
                        );
                      })()}
                    </div>
                  ) : null,
                },
                {
                  inputId: "method",
                  type: FieldTypeEnum.Dropdown,
                  label: "Method",
                  value: methodId,
                  onChange: (value) => {
                    console.log(
                      "Product Edit - Method onChange called with value:",
                      value
                    );
                    setMethodId(value);
                  },
                  options: filteredMethods.map(
                    (method) =>
                      ({
                        OptionId: method.Id,
                        Label: method.Name,
                      } as JC_FieldOption)
                  ),
                  enableSearch: true,
                  overrideClass: styles.dropdownField,
                  isDisabled: JC_Utils.stringNullOrEmpty(materialId),
                  validate: (value) =>
                    JC_Utils.stringNullOrEmpty(value as string)
                      ? "Method is required"
                      : "",
                },
                {
                  inputId: "method-info-container",
                  type: FieldTypeEnum.Custom,
                  customNode: methodId ? (
                    <div
                      className={styles.infoContainer}
                      onClick={handleNavigateToMethod}
                    >
                      {(() => {
                        const method = getSelectedMethod();
                        if (!method) return null;
                        return (
                          <>
                            ${method.CostPrice.toFixed(2)}
                            <span className={styles.separator}>|</span>
                            {method.PercentMarkup?.toFixed(2) || "0.00"}%
                            <span className={styles.separator}>|</span>$
                            {method.SalesPrice?.toFixed(2) || "0.00"}
                          </>
                        );
                      })()}
                    </div>
                  ) : null,
                },
                {
                  inputId: "dimensions",
                  type: FieldTypeEnum.Custom,
                  label: "Dimensions",
                  value: "",
                  readOnly: true,
                  customNode: (
                    <div className={styles.dimensionsContainer}>
                      <div className={styles.dimensionX}>
                        <JC_Field
                          inputId="dimensionX"
                          type={FieldTypeEnum.Number}
                          label="Width (mm)"
                          value={dimensionX.toString()}
                          onChange={(value) => setDimensionX(Number(value))}
                          decimalPlaces={2}
                        />
                        <div className={styles.dimensionSeparator}>×</div>
                      </div>
                      <JC_Field
                        inputId="dimensionY"
                        type={FieldTypeEnum.Number}
                        label="Height (mm)"
                        value={dimensionY.toString()}
                        onChange={(value) => setDimensionY(Number(value))}
                        decimalPlaces={2}
                      />
                      <div className={styles.hideOnTiny}>
                        <JC_Field
                          inputId="area"
                          type={FieldTypeEnum.Number}
                          label="Area (mm²)"
                          value={parseFloat(
                            (dimensionX * dimensionY).toFixed(0)
                          )}
                          decimalPlaces={0}
                          readOnly={true}
                          overrideClass={styles.areaField}
                        />
                      </div>
                    </div>
                  ),
                },
                {
                  inputId: "cost-sale-row",
                  type: FieldTypeEnum.Custom,
                  customNode: (
                    <div className={styles.priceFieldsRow}>
                      <JC_Field
                        inputId="costPrice"
                        type={FieldTypeEnum.Number}
                        label="Cost ($/m²)"
                        value={genCostPrice}
                        decimalPlaces={2}
                        readOnly
                      />
                      <JC_Field
                        inputId="salesPrice"
                        type={FieldTypeEnum.Number}
                        label="Sale ($)"
                        value={getSalePrice}
                        decimalPlaces={2}
                        readOnly
                      />
                    </div>
                  ),
                },
                {
                  inputId: "profit-final-row",
                  type: FieldTypeEnum.Custom,
                  customNode: (
                    <div className={styles.profitFinalRow}>
                      <JC_Field
                        inputId="profitMargin"
                        type={FieldTypeEnum.Number}
                        label="Profit Margin (%)"
                        value={profitMargin}
                        onChange={handleProfitMarginChange}
                        decimalPlaces={2}
                        readOnly={isProfitMarginReadOnly}
                        onClick={() => togglePriceField(true)}
                        inputOverrideClass={
                          isProfitMarginReadOnly ? styles.readOnlyToggle : ""
                        }
                      />
                      <JC_Field
                        inputId="finalSalesPrice"
                        type={FieldTypeEnum.Number}
                        label="Final Sale ($)"
                        value={salesPrice}
                        onChange={handleSalesPriceChange}
                        decimalPlaces={2}
                        readOnly={isFinalSalesPriceReadOnly}
                        onClick={() => togglePriceField(false)}
                        inputOverrideClass={
                          isFinalSalesPriceReadOnly ? styles.readOnlyToggle : ""
                        }
                      />
                    </div>
                  ),
                },
              ]}
            />
          </div>
        )}
      </JC_PageContentContainer>

      {/* Products list */}
      {initialised && (
        <JC_PageContentContainer
          hasBorder={true}
          overrideClass={styles.productsContainer}
        >
          {/* Filter checkboxes */}
          <div className={styles.filterCheckboxesContainer}>
            <JC_Checkbox
              label="Client"
              checked={filterCheckboxes.client}
              onChange={() => handleFilterCheckboxChange("client")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Name"
              checked={filterCheckboxes.name}
              onChange={() => handleFilterCheckboxChange("name")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Material"
              checked={filterCheckboxes.material}
              onChange={() => handleFilterCheckboxChange("material")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Method"
              checked={filterCheckboxes.method}
              onChange={() => handleFilterCheckboxChange("method")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Width"
              checked={filterCheckboxes.width}
              onChange={() => handleFilterCheckboxChange("width")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Height"
              checked={filterCheckboxes.height}
              onChange={() => handleFilterCheckboxChange("height")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Profit Margin"
              checked={filterCheckboxes.profitMargin}
              onChange={() => handleFilterCheckboxChange("profitMargin")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Final Sale"
              checked={filterCheckboxes.finalSale}
              onChange={() => handleFilterCheckboxChange("finalSale")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
          </div>

          <div className={styles.headerContainer}>
            <JC_Title title="Matching Products" />
          </div>

          {productsListLoading ? (
            <JC_Spinner isPageBody />
          ) : (
            <JC_List
              items={filteredProducts}
              headers={getProductsListHeaders()}
              defaultSortKey="Name"
              defaultSortAsc={true}
              row={renderProductRow}
              rowClickCallback={handleProductRowClick}
            />
          )}
        </JC_PageContentContainer>
      )}

      {/* Confirmation Modal */}
      {confirmationModalData && (
        <JC_ModalConfirmation
          width={confirmationModalData.width}
          title={confirmationModalData.title}
          text={confirmationModalData.text}
          isOpen={confirmationModalData != null}
          onCancel={() => setConfirmationModalData(null)}
          submitButtons={confirmationModalData.submitButtons}
          isLoading={confirmationLoading}
        />
      )}

      {/* Auto-fill Modal */}
      <JC_Modal
        isOpen={isAutoFillModalOpen}
        onCancel={handleAutoFillCancel}
        title="Auto-Fill Form Fields"
        width="500px"
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginBottom: "10px",
            }}
          >
            Do you want to auto-fill the form fields based on the product &quot;
            {selectedProductForAutoFill?.Name}&quot;?
          </div>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(4, 1fr)",
              gap: "15px",
              justifyItems: "center",
            }}
          >
            <JC_Checkbox
              label="Client"
              checked={autoFillCheckboxes.client}
              onChange={() => handleAutoFillCheckboxChange("client")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Name"
              checked={autoFillCheckboxes.name}
              onChange={() => handleAutoFillCheckboxChange("name")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Material"
              checked={autoFillCheckboxes.material}
              onChange={() => handleAutoFillCheckboxChange("material")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Method"
              checked={autoFillCheckboxes.method}
              onChange={() => handleAutoFillCheckboxChange("method")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Width"
              checked={autoFillCheckboxes.width}
              onChange={() => handleAutoFillCheckboxChange("width")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Height"
              checked={autoFillCheckboxes.height}
              onChange={() => handleAutoFillCheckboxChange("height")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Profit Margin"
              checked={autoFillCheckboxes.profitMargin}
              onChange={() => handleAutoFillCheckboxChange("profitMargin")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
            <JC_Checkbox
              label="Final Sale"
              checked={autoFillCheckboxes.finalSale}
              onChange={() => handleAutoFillCheckboxChange("finalSale")}
              labelPosition={CheckboxLabelPositionEnum.Top}
            />
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "15px",
              marginTop: "20px",
            }}
          >
            <JC_Button text="Cancel" onClick={handleAutoFillCancel} />
            <JC_Button
              text="Auto-fill"
              onClick={handleAutoFillConfirm}
              isSecondary
            />
          </div>
        </div>
      </JC_Modal>
    </div>
  );
}
