"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../../components/JC_Title/JC_Title";
import JC_Form from "../../../components/JC_Form/JC_Form";
import J<PERSON>_Spinner from "../../../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_PageContentContainer from "../../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_List from "../../../components/JC_List/JC_List";
import JC_Button from "../../../components/JC_Button/JC_Button";
import JC_Tooltip from "../../../components/JC_Tooltip/JC_Tooltip";
import { TooltipPositionEnum } from "../../../enums/TooltipPosition";
import JC_ThreeDotsMenu from "../../../components/JC_ThreeDotsMenu/JC_ThreeDotsMenu";
import JC_ModalConfirmation from "../../../components/JC_ModalConfirmation/JC_ModalConfirmation";
import { ClientModel } from "../../../models/Client";
import { ProductModel } from "../../../models/Product";
import { MaterialModel } from "../../../models/Material";
import { MethodModel } from "../../../models/Method";
import { JC_Get } from "../../../apiServices/JC_Get";
import { JC_Utils } from "../../../Utils";
import { FieldTypeEnum } from "../../../enums/FieldType";
import { JC_ConfirmationModalUsageModel } from "../../../models/ComponentModels/JC_ConfirmationModalUsage";

export default function Page_EditClient({ params }: { params: { id: string } }) {
    const router = useRouter();
    const isNew = params.id === "new";

    const [client, setClient] = useState<ClientModel>(new ClientModel());
    const [products, setProducts] = useState<ProductModel[]>([]);
    const [materials, setMaterials] = useState<MaterialModel[]>([]);
    const [methods, setMethods] = useState<MethodModel[]>([]);
    const [initialised, setInitialised] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>("");

    // Confirmation modal states
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>(null);
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);

    // Field states
    const [name, setName] = useState<string>("");

    // Load client and related data on page load
    useEffect(() => {
        const loadData = async () => {
            try {
                // If editing existing client, load it
                if (!isNew) {
                    const clientResult = await JC_Get<ClientModel>(
                        ClientModel,
                        ClientModel.apiRoute,
                        { id: params.id }
                    );

                    setClient(clientResult);
                    setName(clientResult.Name);

                    // Load products for this client
                    const productsResult = await ProductModel.GetList(undefined, undefined);
                    // Filter products for this client (since we don't have a specific API endpoint for this)
                    const clientProducts = productsResult.ResultList.filter(p => p.ClientId === params.id);
                    setProducts(clientProducts);

                    // Load materials and methods for reference
                    const materialsResult = await MaterialModel.GetList();
                    setMaterials(materialsResult.ResultList);

                    const methodsResult = await MethodModel.GetList();
                    setMethods(methodsResult.ResultList);
                }

                setInitialised(true);
            } catch (error) {
                console.error("Error loading data:", error);
                JC_Utils.showToastError("Failed to load data.");
                setInitialised(true);
            }
        };

        loadData();
    }, [isNew, params.id]);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);
            setErrorMessage("");

            // Update client object with form values
            client.Name = name;

            // Create or update client
            if (isNew) {
                await ClientModel.Create(client);
                JC_Utils.showToastSuccess("Client created successfully.");
            } else {
                await ClientModel.Update(client);
                JC_Utils.showToastSuccess("Client updated successfully.");
            }

            // Navigate back to clients list
            router.push("/clients");
        } catch (error) {
            console.error("Error saving client:", error);
            setErrorMessage("Failed to save client.");
            setIsLoading(false);
        }
    };

    // Handle edit product
    const handleEditProduct = (productId: string) => {
        router.push(`/products/edit/${productId}?clientId=${params.id}`);
    };

    // Handle new product for this client
    const handleNewProduct = () => {
        // Navigate to new product page with client ID as query parameter
        router.push(`/products/edit/new?clientId=${params.id}`);
    };

    // Handle delete client
    const handleDeleteClient = async () => {
        if (isNew) return; // Don't show delete option for new clients

        try {
            // Check if this client has any products
            if (products.length > 0) {
                // Show warning that client has products
                setConfirmationModalData({
                    title: "Cannot Delete Client",
                    text: `This client has ${products.length} product(s). Please delete all products first before deleting the client.`,
                    submitButtons: []
                });
                return;
            }

            // If no products, show delete confirmation
            setConfirmationModalData({
                title: "Delete Client",
                text: `Are you sure you want to delete "${name}"?`,
                submitButtons: [{
                    text: "Delete",
                    onSubmit: async () => {
                        try {
                            setConfirmationLoading(true);
                            await ClientModel.Delete(params.id);
                            setConfirmationLoading(false);
                            setConfirmationModalData(null);
                            JC_Utils.showToastSuccess("Client deleted successfully.");
                            router.push("/clients");
                        } catch (error) {
                            console.error("Error deleting client:", error);
                            setConfirmationLoading(false);
                            JC_Utils.showToastError("Failed to delete client.");
                        }
                    }
                }]
            });
        } catch (error) {
            console.error("Error checking product usage:", error);
            JC_Utils.showToastError("Failed to check if client has products.");
        }
    };

    // Get material name by ID
    const getMaterialName = (materialId: string): string => {
        const material = materials.find(m => m.Id === materialId);
        return material ? material.Name : "Unknown Material";
    };

    // Get material details for tooltip
    const getMaterialDetails = (materialId: string): MaterialModel | undefined => {
        return materials.find(m => m.Id === materialId);
    };

    // Get method name by ID
    const getMethodName = (product: ProductModel): string => {
        // If Ex_MethodName is populated, use it
        if (product.Ex_MethodName) {
            return product.Ex_MethodName;
        }

        // Fallback to finding method by ID
        const method = methods.find(m => m.Id === product.MethodId);
        return method ? method.Name : 'Unknown Method';
    };

    // Get method details for tooltip
    const getMethodDetails = (methodId: string): MethodModel | undefined => {
        return methods.find(m => m.Id === methodId);
    };

    // Generate headers for products list (without client column)
    const getProductsListHeaders = () => {
        return [
            { label: "Name", sortKey: "Name" },
            { label: "Material", sortKey: "MaterialId", hideOnTiny: true },
            { label: "Method", sortKey: "MethodId", hideOnTiny: true },
            { label: "Width", sortKey: "DimensionX", hideOnMedium: true },
            { label: "Height", sortKey: "DimensionY", hideOnMedium: true },
            { label: "Area", sortKey: "Ex_Area", hideOnLarge: true },
            { label: "Profit Margin", sortKey: "ProfitMargin", hideOnSmall: true },
            { label: "Sale", sortKey: "SalesPrice", hideOnTeenyTiny: true },
            { label: "Created At", sortKey: "CreatedAt", hideOnMedium: true }
        ];
    };

    // Render row for products list (without client column)
    const renderProductRow = (product: ProductModel) => {
        return (
            <tr
                key={product.Id}
                onClick={() => handleEditProduct(product.Id)}
            >
                <td>{product.Name}</td>
                <td className={styles.materialCell}>
                    {(() => {
                        const materialName = getMaterialName(product.MaterialId);
                        const material = getMaterialDetails(product.MaterialId);

                        if (!material) return materialName;

                        return (
                            <>
                                {materialName}
                                <JC_Tooltip
                                    content={
                                        <div className={styles.tooltipContent}>
                                            ${material.CostPrice.toFixed(2)}
                                            <span className={styles.separator}>|</span>
                                            {material.PercentMarkup.toFixed(2)}%
                                            <span className={styles.separator}>|</span>
                                            ${material.SalesPrice.toFixed(2)}
                                        </div>
                                    }
                                    position={TooltipPositionEnum.Top}
                                    absoluteFillSpace={true}
                                />
                            </>
                        );
                    })()}
                </td>
                <td className={styles.methodCell}>
                    {(() => {
                        const methodName = getMethodName(product);
                        const method = getMethodDetails(product.MethodId);

                        if (!method) return methodName;

                        return (
                            <>
                                {methodName}
                                <JC_Tooltip
                                    content={
                                        <div className={styles.tooltipContent}>
                                            ${method.CostPrice.toFixed(2)}
                                            <span className={styles.separator}>|</span>
                                            {method.PercentMarkup.toFixed(2)}%
                                            <span className={styles.separator}>|</span>
                                            ${method.SalesPrice.toFixed(2)}
                                        </div>
                                    }
                                    position={TooltipPositionEnum.Top}
                                    absoluteFillSpace={true}
                                />
                            </>
                        );
                    })()}
                </td>
                <td>{Math.round(product.DimensionX)}mm</td>
                <td>{Math.round(product.DimensionY)}mm</td>
                <td>{parseFloat(product.Ex_Area.toFixed(0))}<span dangerouslySetInnerHTML={{ __html: "mm<sup>2</sup>" }} /></td>
                <td>{`${product.ProfitMargin.toFixed(2)}%`}</td>
                <td>${product.SalesPrice.toFixed(2)}</td>
                <td>{new Date(product.CreatedAt).toLocaleDateString() + ' ' + new Date(product.CreatedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true}).replace(' ', '')}</td>
            </tr>
        );
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Clients", path: "/clients" },
                    { label: isNew ? "New Client" : "Edit Client", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true} overrideClass={styles.noBottomMargin}>
                {!isNew && initialised && (
                    <JC_ThreeDotsMenu
                        menuItems={[
                            {
                                label: "Delete",
                                onClick: handleDeleteClient
                            }
                        ]}
                        overrideClass={styles.editPageThreeDotsMenu}
                    />
                )}
                <div className={styles.headerContainer}>
                    <JC_Title title={isNew ? "New Client" : "Edit Client"} />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <div className={styles.formContainer}>
                        <JC_Form
                            submitButtonText="Save"
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                {
                                    inputId: "name",
                                    type: FieldTypeEnum.Text,
                                    label: "Name",
                                    value: name,
                                    onChange: (value) => setName(value),
                                    validate: (value) =>
                                        JC_Utils.stringNullOrEmpty(value as string)
                                            ? "Name is required"
                                            : ""
                                }
                            ]}
                        />
                    </div>
                )}
            </JC_PageContentContainer>

            {/* Products list - only show for existing clients */}
            {!isNew && initialised && (
                <JC_PageContentContainer hasBorder={true} overrideClass={styles.productsContainer}>
                    <div className={styles.headerContainer}>
                        <JC_Title title="Products" />
                    </div>

                    <div style={{ position: 'relative', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <JC_List
                            items={products}
                            headers={getProductsListHeaders()}
                            defaultSortKey="Name"
                            defaultSortAsc={true}
                            row={renderProductRow}
                        />
                        <JC_Button
                            text="+"
                            onClick={handleNewProduct}
                            isSecondary
                            isCircular
                            overrideClass={styles.addButton}
                        />
                    </div>
                </JC_PageContentContainer>
            )}

            {/* Confirmation Modal */}
            {confirmationModalData && (
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />
            )}
        </div>
    );
}