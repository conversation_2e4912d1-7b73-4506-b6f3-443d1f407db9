import { NextRequest, NextResponse } from "next/server";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { GlobalSettingsBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const settings: GlobalSettingsModel[] = await request.json();
        await GlobalSettingsBusiness.CreateList(settings);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
