@import '../../global';

.buttonContainer,
.spinnerContainer {
    height: 44px;
    min-width: 140px;
}

.buttonContainer {
    width: max-content;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: $offWhite;
    outline-color: $offBlack;
    outline-width: $smallBorderWidth;
    outline-style: solid;
    border-radius: $largeBorderRadius;
    font-size: 20px;
    font-weight: bold;
    overflow: visible;
    user-select: none;
    cursor: pointer;
    transition: outline-width  0.15s,
                outline-color  0.35s,
                outline-offset 0.15s;

    // Text
    .buttonText {
        width: max-content;
        overflow: visible;
        color: black;
    }

    // Hover - but not when disabled or secondary
    &:hover:not(.disabled, .secondary) {
        outline-width: $largeBorderWidth;
        outline-color: $primaryColor;
        outline-offset: -1px;
        transition: outline-width  0.04s,
                    outline-color  0.08s,
                    outline-offset 0.04s;
    }

    // Selected
    &.buttonSelected {
        outline-width: $largeBorderWidth;
        outline-color: $pastelSecondaryColor;
        outline-offset: -1px;
        cursor: default !important;
        transition: outline-width 0s,
                    outline-color 0s;
    }

    // Icon
    &.includeIcon {
        column-gap: 8px;
        justify-content: space-between;
        .buttonIcon {
            width: 20px;
            height: auto;
            margin-left: -3px;
        }
        .buttonText {
            position: static;
            transform: translate(0, 0);
            flex-grow: 1;
        }
    }

    // Icon Only
    &.iconOnly {
        min-width: 0;
        padding: 0 15px;
        border-radius: $tinyBorderRadius;
        .buttonIcon {
            margin-left: 0;
        }
    }

    // Icon On Top
    &.iconOnTop {
        flex-direction: column;
        padding: 5px 0;
        height: max-content;
        min-height: 82px;
        row-gap: 5px;
        justify-content: space-between;
        .buttonText {
            flex-grow: 0;
        }
        .buttonIcon {
            margin-top: 2px;
            height: 40px;
            width: auto;
            margin-left: 0;
        }
    }

    // Secondary
    &.secondary {
        outline-color: transparent;
        background-color: $pastelPrimaryColor;
        &:hover:not(.disabled) { background-color: $primaryColor; }
        &.buttonSelected { outline-color: $pastelSecondaryColor !important; }
    }

    // Small
    &.small {
        min-width: 60px;
        height: 34px;
        padding: 0 14px;
        font-size: 14px;
        // font-weight: normal;
        &:hover:not(.disabled) { outline-width: $smallBorderWidth; }
        &.buttonSelected { outline-width: $smallBorderWidth }
    }

    // Circular
    &.circular {
        min-width: 44px;
        width: 44px;
        height: 44px;
        padding: 0;
        border-radius: 50%;
        font-size: 24px;

        .buttonText {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        &:hover:not(.disabled) {
            outline-offset: 0;
        }
    }

    // Disabled
    &.disabled {
        opacity: 0.7;
        cursor: inherit;

        // Only apply grey outline when not secondary
        &:not(.secondary) {
            outline-color: $offBlack !important;
            outline-width: $smallBorderWidth !important;
        }

        // Prevent hover styles from changing the outline
        &:hover {
            // Keep the same outline properties as the disabled state
            // Don't change on hover
            transition: none !important;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 115px;
        height: 36px;
        font-size: 16px;

        .buttonIcon {
            width: 17px !important;
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 95px;
        height: 30px;
        padding: 0 10px;
        font-size: 13px;
        outline-width: $tinyBorderWidth;

        &:hover:not(.disabled) {
            outline-width: $smallBorderWidth;
        }
        &.buttonSelected {
            outline-width: $smallBorderWidth;
        }
        .buttonIcon {
            width: 13px !important;
            margin-left: 0 !important;
            margin-right: -2px !important;
        }
    }
}