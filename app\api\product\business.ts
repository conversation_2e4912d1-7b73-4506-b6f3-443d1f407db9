import { sql } from "@vercel/postgres";

export class ProductBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id:string) {
        // Get the product with method name using a join
        const result = await sql`
            SELECT p."Id",
                   p."ClientId",
                   p."Name",
                   p."MaterialId",
                   p."MethodId",
                   p."DimensionX",
                   p."DimensionY",
                   p."ProfitMargin",
                   p."SalesPrice",
                   p."IsSalesPriceReadOnlyOn",
                   p."CreatedAt",
                   p."ModifiedAt",
                   p."Deleted",
                   m."Name" AS "Ex_MethodName",
                   (p."DimensionX" * p."DimensionY") AS "Ex_Area"
            FROM public."Product" p
            LEFT JOIN public."Method" m ON p."MethodId" = m."Id" AND m."Deleted" = 'False'
            WHERE p."Id" = ${id}
              AND p."Deleted" = 'False'
        `;

        // Convert the result to ProductModel
        const product = result.rows[0];

        return product;
    }

    static async GetAll() {
        // Get all products with method name using a join
        const result = await sql`
            SELECT p."Id",
                   p."ClientId",
                   p."Name",
                   p."MaterialId",
                   p."MethodId",
                   p."DimensionX",
                   p."DimensionY",
                   p."ProfitMargin",
                   p."SalesPrice",
                   p."IsSalesPriceReadOnlyOn",
                   p."CreatedAt",
                   p."ModifiedAt",
                   p."Deleted",
                   m."Name" AS "Ex_MethodName",
                   (p."DimensionX" * p."DimensionY") AS "Ex_Area"
            FROM public."Product" p
            LEFT JOIN public."Method" m ON p."MethodId" = m."Id" AND m."Deleted" = 'False'
            WHERE p."Deleted" = 'False'
            ORDER BY p."CreatedAt" DESC
        `;

        // Convert the result to ProductModel array
        const products = result.rows;

        return products;
    }

    static async GetByClientId(clientId:string) {
        // Get products by client ID with method name using a join
        const result = await sql`
            SELECT p."Id",
                   p."ClientId",
                   p."Name",
                   p."MaterialId",
                   p."MethodId",
                   p."DimensionX",
                   p."DimensionY",
                   p."ProfitMargin",
                   p."SalesPrice",
                   p."IsSalesPriceReadOnlyOn",
                   p."CreatedAt",
                   p."ModifiedAt",
                   p."Deleted",
                   m."Name" AS "Ex_MethodName",
                   (p."DimensionX" * p."DimensionY") AS "Ex_Area"
            FROM public."Product" p
            LEFT JOIN public."Method" m ON p."MethodId" = m."Id" AND m."Deleted" = 'False'
            WHERE p."ClientId" = ${clientId}
              AND p."Deleted" = 'False'
            ORDER BY p."CreatedAt" DESC
        `;

        // Convert the result to ProductModel array
        const products = result.rows;

        return products;
    }

    static async GetByMaterialId(materialId:string) {
        // Get products by material ID with method name and client name using joins
        const result = await sql`
            SELECT p."Id",
                   p."ClientId",
                   p."Name",
                   p."MaterialId",
                   p."MethodId",
                   p."DimensionX",
                   p."DimensionY",
                   p."ProfitMargin",
                   p."SalesPrice",
                   p."IsSalesPriceReadOnlyOn",
                   p."CreatedAt",
                   p."ModifiedAt",
                   p."Deleted",
                   m."Name" AS "Ex_MethodName",
                   c."Name" AS "Ex_ClientName",
                   (p."DimensionX" * p."DimensionY") AS "Ex_Area"
            FROM public."Product" p
            LEFT JOIN public."Method" m ON p."MethodId" = m."Id" AND m."Deleted" = 'False'
            LEFT JOIN public."Client" c ON p."ClientId" = c."Id" AND c."Deleted" = 'False'
            WHERE p."MaterialId" = ${materialId}
              AND p."Deleted" = 'False'
            ORDER BY p."Name"
        `;

        // Convert the result to ProductModel array
        const products = result.rows;

        return products;
    }




}
