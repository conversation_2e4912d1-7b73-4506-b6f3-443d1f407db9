import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { FieldTypeEnum } from "../enums/FieldType";

export class AddressModel extends _Base implements _ModelRequirements {

    static tableName: string = "Address";
    static apiRoute: string = "address";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Line1";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId: string;
    Line1: string;
    Line2: string;
    City: string;
    Postcode: string;
    Country: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        // Add extended field definitions here if needed
    ];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<AddressModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = "";
        this.Line1 = "";
        this.Line2 = "";
        this.City = "";
        this.Postcode = "";
        this.Country = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new AddressModel());
    }

    static jcFieldTypeforField(fieldName: keyof AddressModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "UserId":
                return FieldTypeEnum.Text;
            case "Line1":
                return FieldTypeEnum.Text;
            case "Line2":
                return FieldTypeEnum.Text;
            case "City":
                return FieldTypeEnum.Text;
            case "Postcode":
                return FieldTypeEnum.Text;
            case "Country":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Country} | ${this.City} | ${this.Line1}`;
    }
}
