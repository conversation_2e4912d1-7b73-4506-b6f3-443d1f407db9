= = = = = = = = = = = = = = = =
Environment
= = = = = = = = = = = = = = = =

NAME: N-Grave
EMAIL_PRIMARY: <EMAIL>
EMAIL_FROM: <EMAIL>

# Recommended for most uses
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# For uses requiring a connection without pgbouncer
DATABASE_URL_UNPOOLED=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Parameters for constructing your own connection string
PGHOST=ep-crimson-cell-a7po12ka-pooler.ap-southeast-2.aws.neon.tech
PGHOST_UNPOOLED=ep-crimson-cell-a7po12ka.ap-southeast-2.aws.neon.tech
PGUSER=neondb_owner
PGDATABASE=neondb
PGPASSWORD=npg_iVYD9uCXmg7z

# Parameters for Vercel Postgres Templates
POSTGRES_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_URL_NON_POOLING=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_USER=neondb_owner
POSTGRES_HOST=ep-crimson-cell-a7po12ka-pooler.ap-southeast-2.aws.neon.tech
POSTGRES_PASSWORD=npg_iVYD9uCXmg7z
POSTGRES_DATABASE=neondb
POSTGRES_URL_NO_SSL=postgres://neondb_owner:<EMAIL>/neondb
POSTGRES_PRISMA_URL=postgres://neondb_owner:<EMAIL>/neondb?connect_timeout=15&sslmode=require

RESEND_API_KEY: WWW

NEXT_PUBLIC_STRIPE_PUBLIC_KEY: WWW
STRIPE_SECRET_KEY: WWW
PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET: WWW


= = = = = = = = = = = = = = = =
Code Replacements
= = = = = = = = = = = = = = = =

SITE_DESCRIPTION: Admin site for N-Grave.

PRIMARY_COLOR: 02FF01
SECONDARY_COLOR: FFFFFF
PASTEL_PRIMARY_COLOR: 50E750
PASTEL_SECONDARY_COLOR: FFFFFF
LIGHT_PRIMARY_COLOR: 50E750
LIGHT_SECONDARY_COLOR: FFFFFF
MISCELLANEOUS_COLOR_1: bd7523
MISCELLANEOUS_COLOR_2: 3281ae


= = = = = = = = = = = = = = = =
What You Need To Do
= = = = = = = = = = = = = = = =

In ".env.local", replace these variable values with the values above:
    - Name
    - EMAIL_PRIMARY
    - EMAIL_FROM
    - DATABASE_URL
    - DATABASE_URL_UNPOOLED
    - PGHOST
    - PGHOST_UNPOOLED
    - PGUSER
    - PGDATABASE
    - PGPASSWORD
    - POSTGRES_URL
    - POSTGRES_URL_NON_POOLING
    - POSTGRES_USER
    - POSTGRES_HOST
    - POSTGRES_PASSWORD
    - POSTGRES_DATABASE
    - POSTGRES_URL_NO_SSL
    - POSTGRES_PRISMA_URL
    - RESEND_API_KEY
    - NEXT_PUBLIC_STRIPE_PUBLIC_KEY
    - STRIPE_SECRET_KEY
    - PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET

If these variables are not in the list above, remove them from ".env.local":
    - RESEND_API_KEY
    - NEXT_PUBLIC_STRIPE_PUBLIC_KEY
    - STRIPE_SECRET_KEY
    - PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET
    
In ".env.local", set AUTH_SECRET to a random string between 35-50 characters long container letters, numbers and symbols.

In "app/layout.tsx", replace "<>SITE_DESCRIPTION<>" with "SITE_DESCRIPTION" value above.

Replace each color in global.scss with the above colors.