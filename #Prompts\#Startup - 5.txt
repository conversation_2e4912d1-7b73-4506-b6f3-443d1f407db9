For any tables in "DbSchema.dbml" that do not have an API route in this project, create the route and business for them, exactly how the other routes and business files are setup with
    - Get
    - GetList sub-route
    - Create
    - CreateList sub-route
    - Update
    - UpdateList sub-route
    - Delete
    - DeleteList sub-route
    - Any other sub-routes useful for particular fields, for example the "api/productVariation/updateSortOrder"
    
For every change that is made in this project, make sure you:
    - Use the "JC_..." components wherever can, do not create any new ones.
    - Use global.scss variables wherever can
    - Follow existing patterns as much as possible
    
Then, fix all build errors. Keep building until all errors are fixed, making sure you follow the above requirements.