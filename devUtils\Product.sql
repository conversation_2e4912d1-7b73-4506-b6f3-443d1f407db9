SELECT "product"."Id"
      ,"product"."ClientId"
      ,"client"."Name" "__Client"
      ,"product"."Name"
      -- ,"product"."MaterialId"
      -- ,"material"."Name" "__Material"
      -- ,"product"."MethodId"
      -- ,"method"."Name" "__Method"
      -- ,"product"."DimensionX"
      -- ,"product"."DimensionY"
      ,"product"."ProfitMargin"
      ,"product"."SalesPrice"
      ,"product"."IsSalesPriceReadOnlyOn"
      -- ,"product"."CreatedAt"
      -- ,"product"."ModifiedAt"
      -- ,"product"."Deleted"
FROM public."Product" "product"
INNER JOIN public."Client" "client" ON "product"."ClientId" = "client"."Id"
INNER JOIN public."Material" "material" ON "product"."MaterialId" = "material"."Id"
INNER JOIN public."Method" "method" ON "product"."MethodId" = "method"."Id"
WHERE 1=1
      AND "product"."Id" = 'b5f8c1d2-e3f4-4a5b-9c6d-7e8f9a0b1c2d'
      AND "product"."Deleted" = 'False'
ORDER BY "client"."Name", "product"."Name", "product"."CreatedAt" DESC;

-- Delete
-- UPDATE public."Product"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."Product"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'