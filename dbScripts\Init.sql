
-- Create User table
CREATE TABLE IF NOT EXISTS public."User" (
  "Id" UUID PRIMARY KEY,
  "FirstName" varchar(100) NOT NULL,
  "LastName" varchar(100),
  "Email" varchar(100) NOT NULL,
  "PasswordHash" varchar NOT NULL,
  "LoginFailedAttempts" int NOT NULL DEFAULT 0,
  "LoginLockoutDate" timestamp,
  "ChangePasswordToken" varchar(200),
  "ChangePasswordTokenDate" timestamp,
  "Phone" varchar(20),
  "IsAdmin" boolean NOT NULL DEFAULT FALSE,
  "IsWholesale" boolean NOT NULL DEFAULT FALSE,
  "CompanyName" varchar(200),
  "IsEmailSubscribed" boolean NOT NULL DEFAULT TRUE,
  "IsDiscountUser" boolean NOT NULL DEFAULT FALSE,
  "StripeCustomerId" varchar(100),
  "IsVerified" boolean NOT NULL DEFAULT FALSE,
  "VerificationToken" varchar(200),
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

-- Create Address table
CREATE TABLE IF NOT EXISTS public."Address" (
  "Id" UUID PRIMARY KEY,
  "UserId" UUID NOT NULL,
  "Line1" varchar(200) NOT NULL,
  "Line2" varchar(200),
  "City" varchar(50) NOT NULL,
  "Postcode" varchar(16) NOT NULL,
  "Country" varchar(100) NOT NULL DEFAULT 'Australia',
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  FOREIGN KEY ("UserId") REFERENCES public."User"("Id")
);

-- Create UserPersistedData table
CREATE TABLE IF NOT EXISTS public."UserPersistedData" (
  "Id" UUID NOT NULL,
  "UserId" UUID NOT NULL,
  "Code" varchar(50) NOT NULL,
  "Value" varchar(200) NOT NULL,
  PRIMARY KEY ("Id", "Code"),
  FOREIGN KEY ("UserId") REFERENCES public."User"("Id")
);

-- Create Material table
CREATE TABLE IF NOT EXISTS public."Material" (
  "Id" UUID PRIMARY KEY,
  "Name" varchar(200) NOT NULL,
  "MethodIdsListJson" varchar NOT NULL,
  "CostPrice" decimal NOT NULL,
  "PercentMarkup" decimal NOT NULL,
  "SalesPrice" decimal NOT NULL,
  "IsSalesPriceReadOnlyOn" boolean NOT NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

-- Create Method table
CREATE TABLE IF NOT EXISTS public."Method" (
  "Id" UUID PRIMARY KEY,
  "Name" varchar(200) NOT NULL,
  "CostPrice" decimal NOT NULL,
  "PercentMarkup" decimal NOT NULL,
  "SalesPrice" decimal NOT NULL,
  "IsSalesPriceReadOnlyOn" boolean NOT NULL DEFAULT FALSE,
  "CostPerSecond" decimal NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

-- Create Client table
CREATE TABLE IF NOT EXISTS public."Client" (
  "Id" UUID PRIMARY KEY,
  "Name" varchar(200) NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

-- Create Product table
CREATE TABLE IF NOT EXISTS public."Product" (
  "Id" UUID PRIMARY KEY,
  "ClientId" UUID NOT NULL,
  "Name" varchar(200) NOT NULL,
  "MaterialId" UUID NOT NULL,
  "MethodId" UUID NOT NULL,
  "DimensionX" decimal NOT NULL,
  "DimensionY" decimal NOT NULL,
  "ProfitMargin" decimal NOT NULL,
  "SalesPrice" decimal NOT NULL,
  "IsSalesPriceReadOnlyOn" boolean NOT NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  FOREIGN KEY ("ClientId") REFERENCES public."Client"("Id"),
  FOREIGN KEY ("MaterialId") REFERENCES public."Material"("Id"),
  FOREIGN KEY ("MethodId") REFERENCES public."Method"("Id")
);

-- Create GlobalSettings table
CREATE TABLE IF NOT EXISTS public."GlobalSettings" (
  "Code" varchar(50) PRIMARY KEY,
  "Description" varchar(200) NOT NULL,
  "Value" varchar(200) NOT NULL
);

-- Create OrderStatus table
CREATE TABLE IF NOT EXISTS public."OrderStatus" (
  "Code" varchar(50) PRIMARY KEY,
  "Name" varchar(200) NOT NULL,
  "Description" varchar(200) NOT NULL
);


-- OrderStatus

INSERT INTO public."OrderStatus"
("Code",      "Name",      "Description")
VALUES
('Bag',       'Bag',       'Order is user''s bag/cart and has not been submitted yet.'),
('Submitted', 'Submitted', 'Order has been submitted and paid by the user.'),
('Complete',  'Complete',  'Order is ready for pick up or delivery');


-- GlobalSettings

INSERT INTO public."GlobalSettings"
("Code",                  "Description",                                 "Value")
VALUES
('ForceRefreshAuthToken', 'Force refresh of auth token on next request', '0'),
('DiscountPercentage',    'Percentage discount for discount users',      '10'),
('SiteMaintenanceMode',   'Whether the site is in maintenance mode',     'false');