@import '../../global';

.mainContainer {
    @include listPageMainContainerStyles;

    .headerContainer {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
    }

    .addButton {
        margin-top: 30px;
        div {
            margin-top: -3px;
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .headerContainer {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
    }
}
