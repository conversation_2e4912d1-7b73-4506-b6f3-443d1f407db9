"use client"

import { JC_Utils } from "@/app/Utils";
import styles from "./JC_Checkbox.module.scss";
import React from 'react';
import { CheckboxLabelPositionEnum } from "@/app/enums/TooltipPosition";

export default function JC_Checkbox(_: Readonly<{

    label?: string;
    checked?: boolean;
    onChange?: () => void;
    disabled?: boolean;
    readOnly?: boolean;
    labelPosition?: CheckboxLabelPositionEnum;

}>) {

    // Handle click with readOnly check
    const handleClick = () => {
        if (_.readOnly) return; // Don't trigger onChange if readOnly
        if (_.onChange) _.onChange();
    };

    // Default to Right position if not specified
    const labelPosition = _.labelPosition ?? CheckboxLabelPositionEnum.Right;

    // Determine container class based on label position
    const getPositionClass = () => {
        switch (labelPosition) {
            case CheckboxLabelPositionEnum.Top:
                return styles.labelTop;
            case CheckboxLabelPositionEnum.Bottom:
                return styles.labelBottom;
            case CheckboxLabelPositionEnum.Left:
                return styles.labelLeft;
            case CheckboxLabelPositionEnum.Right:
            default:
                return styles.labelRight;
        }
    };

    // Render label
    const renderLabel = () => {
        if (JC_Utils.stringNullOrEmpty(_.label)) return null;
        return <label>{_.label}</label>;
    };

    // Render checkbox
    const renderCheckbox = () => (
        <div className={styles.checkbox}>
            {_.checked && <div className={styles.innerCheckedSquare} />}
        </div>
    );

    return (
        <div
            className={`
                ${styles.mainContainer}
                ${getPositionClass()}
                ${_.onChange != null && !_.readOnly ? styles.clickable : ""}
                ${_.readOnly ? styles.readOnly : ""}
            `}
            onClick={handleClick}
        >
            {labelPosition === CheckboxLabelPositionEnum.Top && renderLabel()}
            {labelPosition === CheckboxLabelPositionEnum.Left && renderLabel()}

            {renderCheckbox()}

            {labelPosition === CheckboxLabelPositionEnum.Right && renderLabel()}
            {labelPosition === CheckboxLabelPositionEnum.Bottom && renderLabel()}
        </div>
    );
}