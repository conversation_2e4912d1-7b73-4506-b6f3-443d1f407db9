import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation';
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: `${process.env.NAME} - Login/Register`,
    description: "Change your password."
};

export default async function Layout_LoginRegister(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Check if user is already logged in
    const session = await auth();
    if (session) {
        // If logged in, redirect to account page
        redirect("/account");
    }

    return _.children;
}
