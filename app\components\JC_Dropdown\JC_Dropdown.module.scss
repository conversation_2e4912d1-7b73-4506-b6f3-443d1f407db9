@import '../../global';

$leftGap: 10px;

.label {
    margin-bottom: 6px;
    padding-left: 10px;
    text-align: left;
    font-size: 15px;
    font-weight: bold;

    // Error Message
    .errorSpan {
        color: $errorColor;
        font-weight: bold;
        padding-left: 10px;
        position: absolute;
        // opacity: 0;
        // animation: errorFadeOutAnimation 2.5s;
    }
}

.mainContainer {
    position: relative;
    width: 100%;
    height: max-content;
    user-select: none;
    color: $offBlack;



    .mainButton {
        @include dropdownOptionContainer;
        box-sizing: border-box;
        position: relative;
        cursor: pointer;

        .optionIcon {
            @include dropdownIcon;
        }

        .chevronIcon {
            position: absolute;
            right: 16px;
            top: 50%; transform: translateY(-50%);
            width: 20px;
            height: auto;
        }

        .searchBox {
            position: absolute;
            left: 5px;
            top: 50%; transform: translateY(-50%);
            border: none;
            outline: none;
            width: 80%;
            height: 70%;
            padding-left: 10px;
            background-color: $offWhite;
        }

        // Read-Only state
        &.readOnly {
            background-color: $lightGrey;
            cursor: default;
        }
    }

    .dropdown {
        @include dropdownDropdown;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); // Add shadow for better visibility
        z-index: 1000 !important; // Force high z-index
        .optionIcon {
            @include dropdownIcon;
        }
    }

    // Styles for the portal dropdown
    :global(.dropdownPortal) {
        .optionIcon {
            @include dropdownIcon;
        }
    }

    // Dropdown option styles
    .dropdownOption {
        @include dropdownOptionContainer;
        margin-bottom: 4px;
        border-color: $primaryColor;
        cursor: pointer;
        &.selected {
            background-color: $greyHover;
            cursor: default;
        }
        &:hover { background-color: $greyHover; }
        &:last-child { margin-bottom: 0; }
    }

}