import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { UserPersistedDataBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// Get all UserPersistedData
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const result = await UserPersistedDataBusiness.GetAll();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
