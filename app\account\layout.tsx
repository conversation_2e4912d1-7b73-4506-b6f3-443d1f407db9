import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation';
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: `${process.env.NAME} - Account`,
    description: "Edit your account info."
};

export default async function Layout_Account(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Check if user is logged in
    const session = await auth();
    if (!session) {
        // If not logged in, redirect to login/register page
        redirect("/loginRegister");
    }

    return _.children;
}
