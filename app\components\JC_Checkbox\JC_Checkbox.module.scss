@import "../../global";

$size: 13px;
$innerGap: 3px;

.mainContainer {
  width: max-content;
  display: flex;
  align-items: center;
  gap: 12px;
  user-select: none;
  &.clickable {
    &,
    .checkbox,
    label {
      cursor: pointer !important;
    }
  }

  // Label position classes
  &.labelTop {
    flex-direction: column;
    align-items: center;
  }

  &.labelRight {
    flex-direction: row;
    align-items: center;
  }

  &.labelBottom {
    flex-direction: column-reverse;
    align-items: center;
  }

  &.labelLeft {
    flex-direction: row-reverse;
    align-items: center;
  }

  // Read-Only state
  &.readOnly {
    opacity: 0.7;
    cursor: default;

    .checkbox,
    label {
      cursor: default;
    }
  }

  // Label styling to match JC_Field
  label {
    color: $secondaryColor;
    font-size: $defaultFontSize;
    font-weight: bold;
  }

  .checkbox {
    width: $size;
    height: $size;
    outline: solid $tinyBorderWidth $primaryColor;
    border-radius: 1px;
    position: relative;

    .innerCheckedSquare {
      position: absolute;
      margin: $innerGap;
      z-index: 999;
      width: calc($size - 2 * $innerGap);
      height: calc($size - 2 * $innerGap);
      border-radius: 1px;
      background-color: $primaryColor;
    }
  }
}
