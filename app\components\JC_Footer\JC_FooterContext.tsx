"use client"

import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from "react";

// Define the context type
interface FooterContextType {
  menuOpen: boolean;
  toggleMenu: () => void;
  closeMenu: () => void;
  menuRef: React.RefObject<HTMLDivElement>;
  burgerRef: React.RefObject<HTMLDivElement>;
}

// Create a default implementation for when the context is used outside of the provider
const defaultFooterContext: FooterContextType = {
  menuOpen: false,
  toggleMenu: () => {},
  closeMenu: () => {},
  menuRef: { current: null },
  burgerRef: { current: null }
};

// Create the context with the default value
const FooterContext = createContext<FooterContextType>(defaultFooterContext);

// Create a provider component
export function FooterProvider({ children }: { children: ReactNode }) {
  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const burgerRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const closeMenu = () => {
    setMenuOpen(false);
  };

  // Handle clicks outside the menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only process if menu is open
      if (!menuOpen) return;

      // Check if the click was outside both the menu and burger button
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        burgerRef.current &&
        !burgerRef.current.contains(event.target as Node)
      ) {
        closeMenu();
      }
    };

    // Add event listener when menu is open
    if (menuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuOpen]);

  return (
    <FooterContext.Provider value={{ menuOpen, toggleMenu, closeMenu, menuRef, burgerRef }}>
      {children}
    </FooterContext.Provider>
  );
}

// Create a hook to use the context
export function useFooter() {
  return useContext(FooterContext);
}
