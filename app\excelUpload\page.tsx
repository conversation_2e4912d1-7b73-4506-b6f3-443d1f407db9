"use client"

import styles from "./page.module.scss";
import { useState, useRef, useCallback, useEffect } from "react";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_List from "../components/JC_List/JC_List";
import { JC_Utils } from "../Utils";
import * as XLSX from 'xlsx';

export default function Page_ExcelUpload() {
    // State for drag and drop
    const [isDragActive, setIsDragActive] = useState<boolean>(false);

    // State for the uploaded data
    const [tableData, setTableData] = useState<any[]>([]);
    const [tableHeaders, setTableHeaders] = useState<any[]>([]);

    // Ref for the drop zone
    const dropZoneRef = useRef<HTMLDivElement>(null);

    // Handle drag events
    const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragActive(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragActive(false);
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isDragActive) {
            setIsDragActive(true);
        }
    }, [isDragActive]);

    // Process the file data
    const processFileData = useCallback((data: ArrayBuffer) => {
        try {
            // Read the workbook
            const workbook = XLSX.read(data, { type: 'array' });

            // Get the first worksheet
            const worksheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[worksheetName];

            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // Extract headers and data
            if (jsonData.length > 0) {
                const headers = jsonData[0] as string[];
                const rows = jsonData.slice(1) as any[][];

                // Create header objects for JC_List with consistent spacing format
                const listHeaders = headers.map(header => ({
                    label: header,
                    sortKey: header
                }));

                // Create data objects with properties matching headers
                const listData = rows.map(row => {
                    const rowData: any = {};
                    headers.forEach((header, index) => {
                        rowData[header] = row[index] !== undefined ? row[index] : '';
                    });
                    return rowData;
                });

                setTableHeaders(listHeaders);
                setTableData(listData);

                // Show success toast
                JC_Utils.showToastSuccess('File uploaded successfully!');
            } else {
                JC_Utils.showToastWarning('The file appears to be empty.');
            }
        } catch (error) {
            console.error('Error processing file:', error);
            JC_Utils.showToastError('Error processing file. Please try again with a valid Excel or CSV file.');
        }
    }, []);

    // Handle file drop
    const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragActive(false);

        const files = e.dataTransfer.files;
        if (files.length) {
            const file = files[0];
            const fileExtension = file.name.split('.').pop()?.toLowerCase();

            // Check if file is Excel or CSV
            if (fileExtension === 'xlsx' || fileExtension === 'xls' || fileExtension === 'csv') {
                const reader = new FileReader();
                reader.onload = (event) => {
                    if (event.target?.result) {
                        processFileData(event.target.result as ArrayBuffer);
                    }
                };
                reader.readAsArrayBuffer(file);
            } else {
                JC_Utils.showToastWarning('Please upload an Excel (.xlsx, .xls) or CSV (.csv) file.');
            }
        }
    }, [processFileData]);

    // Handle file selection via click
    const handleFileSelect = useCallback(() => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls,.csv';
        input.onchange = (e: Event) => {
            const files = (e.target as HTMLInputElement).files;
            if (files && files.length) {
                const file = files[0];
                const reader = new FileReader();
                reader.onload = (event) => {
                    if (event.target?.result) {
                        processFileData(event.target.result as ArrayBuffer);
                    }
                };
                reader.readAsArrayBuffer(file);
            }
        };
        input.click();
    }, [processFileData]);

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Tools", path: "/tools" },
                    { label: "Excel Upload", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer maxWidth={1200} hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Excel Upload" />
                </div>

                <div className={styles.dropZoneContainer}>
                    <div
                        ref={dropZoneRef}
                        className={`${styles.dropZone} ${isDragActive ? styles.dragActive : ''}`}
                        onDragEnter={handleDragEnter}
                        onDragLeave={handleDragLeave}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                        onClick={handleFileSelect}
                    >
                        <div className={styles.dropZoneText}>
                            Drag and drop an Excel or CSV file here
                        </div>
                        <div className={styles.dropZoneSubtext}>
                            or click to select a file
                        </div>
                    </div>
                </div>

                {tableData.length > 0 && (
                    <div className={styles.tableContainer}>
                        <JC_List
                            headers={tableHeaders}
                            items={tableData}
                            defaultSortKey={tableHeaders[0]?.sortKey}
                            defaultSortAsc={true}
                            row={(item) => (
                                <tr key={Math.random().toString()}>
                                    {tableHeaders.map((header) => (
                                        <td key={header.sortKey}>{item[header.sortKey]}</td>
                                    ))}
                                </tr>
                            )}
                        />
                    </div>
                )}
            </JC_PageContentContainer>
        </div>
    );
}
