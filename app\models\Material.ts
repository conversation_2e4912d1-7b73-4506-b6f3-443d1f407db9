import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class MaterialModel extends _Base implements _ModelRequirements {

    static tableName: string = "Material";
    static apiRoute: string = "material";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    static cacheMinutes_get = 10080; // 1 week
    static cacheMinutes_getList = 10080; // 1 week

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static async Get(id: string) {
        return await JC_Get<MaterialModel>(MaterialModel, MaterialModel.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${MaterialModel.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?: JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<MaterialModel>(MaterialModel, `${MaterialModel.apiRoute}/getList`, paging, undefined, abortSignal);
    }
    static async Create(data: MaterialModel) {
        return await JC_Put<MaterialModel>(MaterialModel, MaterialModel.apiRoute, data);
    }
    static async CreateList(dataList: MaterialModel[]) {
        return await JC_PutRaw<MaterialModel[]>(`${MaterialModel.apiRoute}/createList`, dataList, undefined, "Material");
    }
    static async Update(data: MaterialModel) {
        return await JC_Post<MaterialModel>(MaterialModel, MaterialModel.apiRoute, data);
    }
    static async UpdateList(dataList: MaterialModel[]) {
        return await JC_PostRaw<MaterialModel[]>(`${MaterialModel.apiRoute}/updateList`, dataList, undefined, "Material");
    }
    static async Delete(id: string) {
        return await JC_Delete(MaterialModel, MaterialModel.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${MaterialModel.apiRoute}/deleteList`, { ids }, undefined, "Material");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    MethodIdsListJson: string;
    CostPrice: number;
    PercentMarkup: number;
    SalesPrice: number;
    IsSalesPriceReadOnlyOn: boolean;

    // Extended
    Ex_MethodNames: string[];
    Ex_MethodsCount: number;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [
        // Add extended field definitions here if needed
    ];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<MaterialModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        this.MethodIdsListJson = "[]";
        this.CostPrice = 0;
        this.PercentMarkup = 0;
        this.SalesPrice = 0;
        this.IsSalesPriceReadOnlyOn = false;
        // Extended
        this.Ex_MethodNames = [];
        this.Ex_MethodsCount = 0;

        Object.assign(this, init);

        // Ensure number fields are actually numbers
        this.CostPrice = Number(this.CostPrice);
        this.PercentMarkup = Number(this.PercentMarkup);
        this.SalesPrice = Number(this.SalesPrice);
        this.IsSalesPriceReadOnlyOn = Boolean(this.IsSalesPriceReadOnlyOn);
        this.Ex_MethodsCount = Number(this.Ex_MethodsCount);
    }

    static getKeys() {
        return Object.keys(new MaterialModel());
    }

    static jcFieldTypeforField(fieldName: keyof MaterialModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "Name":
                return FieldTypeEnum.Text;
            case "MethodIdsListJson":
                return FieldTypeEnum.Custom;
            case "CostPrice":
                return FieldTypeEnum.Number;
            case "PercentMarkup":
                return FieldTypeEnum.Number;
            case "SalesPrice":
                return FieldTypeEnum.Number;
            case "IsSalesPriceReadOnlyOn":
                return FieldTypeEnum.Custom;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}
