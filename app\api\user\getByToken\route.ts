import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { UserBusiness } from "../business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userToken = params.get("userToken")!;
        const result = await UserBusiness.GetByToken(userToken);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}