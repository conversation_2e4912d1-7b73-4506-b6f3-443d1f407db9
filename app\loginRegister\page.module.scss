@import '../global';

.mainContainer {
    padding-top: 10vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    background-color: transparent;
    overflow-y: auto;
    box-sizing: border-box;

    .logoContainer {
        margin-bottom: 70px;
        position: relative;
        z-index: 2;

        .logo {
            width: 380px;
            height: auto;
            max-width: 100%;
        }
    }

    .loginContainer {
        position: relative;
        z-index: 2;
        background-color: $offBlack;
        margin-bottom: 0;
        min-height: max-content !important;
        margin-bottom: 120px;

        // Add padding at the bottom for the register form
        padding-bottom: 20px;

        @media (max-height: 900px) {
            max-height: 75vh; // Smaller max height on shorter screens
        }

        @media (max-height: 800px) {
            max-height: 70vh; // Even smaller max height on very short screens
        }
    }

    // Tab Body
    .tabBody {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;

        // Small Text
        .smallTextButton {
            font-weight: bold;
            user-select: none;
            cursor: pointer;
            color: $secondaryColor;
            &:hover {
                color: $primaryColor;
            }
        }

        // Password Requirements Field
        .passwordRequirementsField {
            margin-top: -15px;
            margin-bottom: -15px;
        }
    }

}