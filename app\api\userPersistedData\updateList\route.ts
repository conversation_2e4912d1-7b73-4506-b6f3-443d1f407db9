import { NextRequest, NextResponse } from "next/server";
import { UserPersistedDataModel } from "@/app/models/UserPersistedData";
import { UserPersistedDataBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const dataList:UserPersistedDataModel[] = await request.json();
        await UserPersistedDataBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
