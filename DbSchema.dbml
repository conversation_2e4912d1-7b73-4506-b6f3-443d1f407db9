
// - User - //

Table User {
  Id                      UUID         [not null, PK]
  FirstName               varchar(100) [not null]
  LastName                varchar(100) [    null]
  Email                   varchar(100) [not null]
  PasswordHash            varchar      [not null]
  LoginFailedAttempts     int          [not null, default: 0]
  LoginLockoutDate        timestamp    [    null]
  ChangePasswordToken     varchar(200) [    null]
  ChangePasswordTokenDate timestamp    [    null]
  Phone                   varchar(20)  [    null]
  IsAdmin                 boolean      [not null, default: FALSE, note: 'An admin user can make a user admin through Users list page.']
  IsWholesale             boolean      [not null, default: FALSE, note: 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.']
  CompanyName             varchar(200) [    null,                 note: 'Required field for wholesale users.']
  IsEmailSubscribed       boolean      [not null, default: TRUE]
  IsDiscountUser          boolean      [not null, default: FALSE, note: 'Discount amount set in [GlobalSettings].']
  StripeCustomerId        varchar(100) [    null,                 note: 'Set when user makes payment for first time since their Stripe customer account is created.']
  IsVerified              boolean      [not null, default: FALSE]
  VerificationToken       varchar(200) [    null]
  CreatedAt               timestamp    [not null, default: 'now()']
  ModifiedAt              timestamp    [    null]
  Deleted                 boolean      [not null, default: FALSE]
}

// - Address - //

Table Address {
  Id         UUID         [not null, PK]
  UserId     UUID         [not null, ref: > User.Id]
  Line1      varchar(200) [not null]
  Line2      varchar(200) [    null]
  City       varchar(50)  [not null]
  Postcode   varchar(16)  [not null]
  Country    varchar(100) [not null, default: 'Australia']
  CreatedAt  timestamp    [not null, default: 'now()']
  ModifiedAt timestamp    [    null]
  Deleted    boolean      [not null, default: FALSE]
}

// - UserPersistedData - //

Table UserPersistedData {
  Id     UUID         [not null, PK]
  UserId UUID         [not null, ref: > User.Id]
  Code   varchar(50)  [not null, PK]
  Value  varchar(200) [not null]
}

// - Material - //

Table Material {
  Id                     UUID         [not null, PK]
  Name                   varchar(200) [not null]
  MethodIdsListJson      varchar      [not null]
  CostPrice              decimal      [not null]
  PercentMarkup          decimal      [not null]
  SalesPrice             decimal      [not null]
  IsSalesPriceReadOnlyOn boolean      [not null, default: 0]
  CreatedAt              timestamp    [not null, default: 'now()']
  ModifiedAt             timestamp    [    null]
  Deleted                boolean      [not null, default: FALSE]
}

// - Method - //

Table Method {
  Id                     UUID         [not null, PK]
  Name                   varchar(200) [not null]
  CostPrice              decimal      [not null]
  PercentMarkup          decimal      [not null]
  SalesPrice             decimal      [not null]
  IsSalesPriceReadOnlyOn boolean      [not null, default: 0]
  CostPerSecond          decimal      [not null]
  CreatedAt              timestamp    [not null, default: 'now()']
  ModifiedAt             timestamp    [    null]
  Deleted                boolean      [not null, default: FALSE]
}

// - Client - //

Table Client {
  Id         UUID         [not null, PK]
  Name       varchar(200) [not null]
  CreatedAt  timestamp    [not null, default: 'now()']
  ModifiedAt timestamp    [    null]
  Deleted    boolean      [not null, default: FALSE]
}

// - Product - //

Table Product {
  Id                     UUID      [not null, PK]
  ClientId               UUID      [not null, ref: > Client.Id]
  Name                   varchar(200) [not null]
  MaterialId             UUID      [not null, ref: > Material.Id]
  MethodId               UUID      [not null, ref: > Method.Id]
  DimensionX             decimal   [not null]
  DimensionY             decimal   [not null]
  ProfitMargin           decimal   [not null]
  SalesPrice             decimal   [not null]
  IsSalesPriceReadOnlyOn boolean   [not null, default: 0]
  CreatedAt              timestamp [not null, default: 'now()']
  ModifiedAt             timestamp [    null]
  Deleted                boolean   [not null, default: FALSE]
}

// - Global Settings - //

Table GlobalSettings {
  Code        varchar(50)  [not null, PK]
  Description varchar(200) [not null]
  Value       varchar(200) [not null]
}

// - Order Status - //

Table OrderStatus {
  Code        varchar(50)  [not null, PK]
  Name        varchar(200) [not null]
  Description varchar(200) [not null]
}