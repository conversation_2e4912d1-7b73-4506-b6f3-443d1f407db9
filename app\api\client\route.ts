import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ClientModel } from "@/app/models/Client";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id"
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        let result = await JC_Utils_Business.sqlGet(ClientModel, id);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - CREATE - //
// ---------- //

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const client:ClientModel = await request.json();
        await JC_Utils_Business.sqlCreate(ClientModel, client);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - UPDATE - //
// ---------- //

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const client:ClientModel = await request.json();
        await JC_Utils_Business.sqlUpdate(ClientModel, client);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - DELETE - //
// ---------- //

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        await JC_Utils_Business.sqlDelete(ClientModel, id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});