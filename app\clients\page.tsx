"use client"

import styles from "./page.module.scss";
import { useRouter } from "next/navigation";
import J<PERSON>_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_List from "../components/JC_List/JC_List";
import JC_Tooltip from "../components/JC_Tooltip/JC_Tooltip";
import { TooltipPositionEnum } from "../enums/TooltipPosition";

import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";
import { ClientModel } from "../models/Client";

export default function Page_Clients() {
    const router = useRouter();

    // Handle edit client
    function handleEditClient(id: string) {
        router.push(`/clients/edit/${id}`);
    }

    // Handle new client
    function handleNewClient() {
        router.push("/clients/edit/new");
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Clients", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Clients" />
                </div>

                <JC_List
                    service={ClientModel.GetList}
                            headers={[
                                { label: "Name",       sortKey: "Name" },
                                { label: "Products",   sortKey: "Ex_ProductsCount", hideOnTiny: true },
                                { label: "Created At", sortKey: "CreatedAt", hideOnTiny: true }
                            ]}
                            defaultSortKey="Name"
                            defaultSortAsc={true}
                            row={(client) => (
                                <tr
                                    key={client.Id}
                                    onClick={() => handleEditClient(client.Id)}
                                >
                                    <td>{client.Name}</td>
                                    <td className={styles.productsCell}>
                                        {(() => {
                                            if (client.Ex_ProductsCount === 0) {
                                                return 0;
                                            }

                                            return (
                                                <>
                                                    <div className={styles.productCount}>
                                                        {client.Ex_ProductsCount}
                                                    </div>
                                                    <JC_Tooltip
                                                        content={
                                                            <div className={styles.productTooltip}>
                                                                {client.Ex_ProductNames.map((name, index) => (
                                                                    <div key={index} className={styles.productName}>
                                                                        {name}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        }
                                                        position={TooltipPositionEnum.Top}
                                                        absoluteFillSpace={true}
                                                    />
                                                </>
                                            );
                                        })()}
                                    </td>
                                    <td>{new Date(client.CreatedAt).toLocaleDateString() + ' ' + new Date(client.CreatedAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true}).replace(' ', '')}</td>
                                </tr>
                            )}
                />
                <JC_Button
                    text="+"
                    onClick={handleNewClient}
                    isSecondary
                    isCircular
                    overrideClass={styles.addButton}
                />
            </JC_PageContentContainer>
        </div>
    );
}
