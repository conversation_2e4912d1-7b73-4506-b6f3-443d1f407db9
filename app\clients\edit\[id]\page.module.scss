@import '../../../global';

.mainContainer {
    @include editPageMainContainerStyles;

    .pageWrapper {
        width: auto;
        display: flex;
        flex-direction: column;
    }

    .editPageThreeDotsMenu {
        @include editPageThreeDotsMenu;
    }

    .headerContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .formContainer {
        width: 100%;
        margin-top: 20px;
    }

    .noBottomMargin {
        margin-bottom: 0;
    }

    .productsContainer {
        margin-top: 30px;
    }

    .addButton {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 10;
    }
}

.materialCell, .methodCell {
    position: relative;
    cursor: pointer;
}

.tooltipContent {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 12px 16px;
    border-radius: $tinyBorderRadius;
    background-color: rgba($offBlack, 0.5);
    color: $white;
    text-align: center;

    .separator {
        margin: 0 8px;
        opacity: 0.6;
        color: $primaryColor;
        font-weight: bold;
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .headerContainer {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
    }
}
