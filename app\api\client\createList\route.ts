import { NextRequest, NextResponse } from "next/server";
import { ClientModel } from "@/app/models/Client";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const clients:ClientModel[] = await request.json();
        for (const client of clients) {
            await JC_Utils_Business.sqlCreate(ClientModel, client);
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
