= = = = = = = = = = = = = = = =
Pages
= = = = = = = = = = = = = = = =

The site main background is offBlack.

Each page has a main container in the middle which wraps content, it is offBlack background with primarycolor border and primary color slight shadow on all sides.

Home page "/" shows a grid of tiles: "Materials", "Methods", "Clients", "Products". Click each to go to their list page.

Each list page loads lsit of corresponding records from db. Clicking an item takes user to "editMaterial" (or corresponding name) page, or can click "New Material" (or corresponding).

Each "edit..." page has simple form with all required fields. Any field that is a foreign key shows as a dropdown where the list is loaded in the initial useEffect with Promise.all since there are now multiple calls.

Every page should show spinner while loading. KNow we are loading when !initialised.

With the Percent Markup and Sales Price fields, the Sales Price is readonly by default. <PERSON><PERSON><PERSON> has slight miscellaneous colour hover and when clicked teh readonly switches to the Percent Markup and Sales Price becomes editable. Whichever field is read-only is calculated based on change of the other field. The read only field here is saved to the db as NULL so we know which field is read-only when reload page. The field here that is editable is required.

Completely hide the footer on every page by adjusting the Layout file.
On the footer, only have primary colour burger button at bottom left which animate expands list of main pages.


= = = = = = = = = = = = = = = =
What You Need To Do
= = = = = = = = = = = = = = = =

Implement all the front-end and back-end for the above.

For every change that is made in this project, make sure you:
    - Use existing components as much as you can, it is very important that you do not create any new ones, just edit the existing components for my requirements.
    - Use global.scss variables wherever can.
    - Follow existing patterns as much as possible
    - Make sure any utils functions created are put into Utils.ts in a new or existing class.

Then, fix all build errors. Keep building until all errors are fixed, making sure you follow the above requirements.