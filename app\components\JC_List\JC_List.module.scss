@import '../../global';

.mainContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.table {
    margin-top: 14px;
    width: auto;
    border-collapse: collapse;
    color: $white;

    th, td {
        padding: 12px 30px;
        text-align: center;
        border-bottom: $smallBorderWidth solid rgba($primaryColor, 0.3);
        white-space: normal;
        word-wrap: break-word;
        max-width: 300px; /* Limit width to force wrapping */
    }

    th {
        color: $primaryColor;
        font-weight: bold;
        background-color: transparent;
        text-align: center;
        cursor: pointer;
        position: relative;
        user-select: none;
        white-space: normal;
        width: auto;
        word-wrap: break-word;

        /* Sortable headers are centered by default */

        .headerLabelContainer {
            position: relative;
            width: max-content;
            margin: 0 auto;
        }

        .headerLabel {
            width: max-content;
            height: max-content;
            display: inline-block;
            text-align: center;
        }

        .sortIndicator {
            display: inline-block;
            position: absolute;
            right: -20px;
            top: 41%;
            transform: translateY(-50%);
            z-index: 1; /* Ensure it's above other elements */
            filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
        }
    }

    thead tr:hover {
        background-color: transparent;
    }

    td {
        text-align: center;
    }

    tbody tr {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba($primaryColor, 0.1);
        }
    }

    tbody .tableRow {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba($primaryColor, 0.1);
        }
    }

    .noDataRow {
        cursor: default;

        &:hover {
            background-color: transparent;
        }

        td {
            border-bottom: none;
        }
    }

    .noDataCell {
        color: $white;
        text-align: center;
        padding-top: 20px;
        font-weight: bold;
    }
}

.spinnerContainer {
    height: 44px;
}

.tableSpinnerOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($offBlack, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.placeholderRow {
    pointer-events: none;
    visibility: hidden;

    &:hover {
        background-color: transparent;
    }

    td {
        border-bottom-color: transparent !important;
    }
}

// Responsive column hiding
@media (max-width: $largeScreenSize) {
    .hideOnLarge {
        display: none !important;
    }
}

@media (max-width: $mediumScreenSize) {
    .hideOnMedium {
        display: none !important;
    }
}

@media (max-width: $smallScreenSize) {
    .hideOnSmall {
        display: none !important;
    }
}

@media (max-width: $tinyScreenSize) {
    .hideOnTiny {
        display: none !important;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .hideOnTeenyTiny {
        display: none !important;
    }
}

.buttonContainer {
    min-width: 140px;
    width: max-content;
    height: 44px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: $offWhite;
    outline-color: $primaryColor;
    outline-width: $smallBorderWidth;
    outline-style: solid;
    border-radius: $largeBorderRadius;
    font-size: 20px;
    font-weight: bold;
    overflow: visible;
    user-select: none;
    cursor: pointer;
    transition: outline-width  0.15s,
                outline-color  0.35s,
                outline-offset 0.15s;

    // Text
    .buttonText {
        width: max-content;
        overflow: visible;
        color: black;
    }

    // Hover
    &:hover {
        outline-width: $largeBorderWidth;
        outline-color: $secondaryColor;
        outline-offset: -1px;
        transition: outline-width  0.04s,
                    outline-color  0.08s,
                    outline-offset 0.04s;
    }

    // Selected
    &.buttonSelected {
        outline-width: $largeBorderWidth;
        outline-color: $pastelSecondaryColor;
        outline-offset: -1px;
        cursor: default !important;
        transition: outline-width 0s,
                    outline-color 0s;
    }

    // Icon
    &.includeIcon {
        column-gap: 8px;
        justify-content: space-between;
        .buttonIcon {
            width: 20px;
            height: auto;
            margin-left: -5px;
        }
        .buttonText {
            position: static;
            transform: translate(0, 0);
            flex-grow: 1;
        }
    }

    // Icon Only
    &.iconOnly {
        min-width: 0;
        padding: 0 15px;
        border-radius: $tinyBorderRadius;
        .buttonIcon {
            margin-left: 0;
        }
    }

    // Icon On Top
    &.iconOnTop {
        flex-direction: column;
        padding: 5px 0;
        height: max-content;
        min-height: 82px;
        row-gap: 5px;
        justify-content: space-between;
        .buttonText {
            flex-grow: 0;
        }
        .buttonIcon {
            margin-top: 2px;
            height: 40px;
            width: auto;
            margin-left: 0;
        }
    }

    // Secondary
    &.secondary {
        outline-color: transparent;
        background-color: $pastelPrimaryColor;
        &:hover { outline-color: $secondaryColor; }
        &.buttonSelected { outline-color: $pastelSecondaryColor !important; }
    }

    // Small
    &.small {
        min-width: 60px;
        height: 34px;
        padding: 0 14px;
        font-size: 14px;
        // font-weight: normal;
        &:hover { outline-width: $smallBorderWidth; }
        &.buttonSelected { outline-width: $smallBorderWidth }
    }

    // Disabled
    &.disabled {
        outline: none !important;
        // background-color: $greyHover;
        opacity: 0.7;
        cursor: inherit;
        &:hover {
            outline-width: $smallBorderWidth;
            outline-color: $primaryColor;
        }
    }
}

// Paging Controls
.pagingControlsContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 20px;

    .pageInfo {
        text-align: center;
        margin-top: 5px;
        color: $primaryColor;
        font-size: 12px;
        font-weight: normal;
    }

    .pagingButtons {
        margin-top: 7px;
        display: flex;
        align-items: center;
        gap: 9px;

        .pagingButton {
            min-width: 40px;
            height: 40px;
            padding: 8px 12px;
            border: 2px solid $grey;
            background-color: $white;
            color: $primaryColor;
            font-size: 14px;
            font-weight: normal;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
                border-color: $secondaryColor;
                background-color: rgba($secondaryColor, 0.1);
            }

            &:disabled {
                opacity: 0.9;
                cursor: not-allowed;
                background-color: $lightGrey;
            }

            &.currentPage {
                background-color: $primaryColor;
                color: $white;
                border-color: transparent;
                cursor: default;

                &:hover {
                    background-color: $primaryColor;
                    border-color: $primaryColor;
                }
            }

            &.previousButton,
            &.nextButton,
            &.firstButton,
            &.lastButton {
                width: 40px;
                height: 40px;
                color: $secondaryColor;
                font-weight: 500;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2px;
            }
            &.firstButton,
            &.lastButton {
                gap: 0px;
            }
        }

        .ellipsis {
            color: $primaryColor;
            font-size: 16px;
            padding: 0 4px;
        }

        .pagingSpacer {
            width: 5.5px;
            height: 1px;
        }

        // Chevron styles for navigation buttons
        .chevronLeft {
            width: 12px;
            height: auto;
            transform: rotate(90deg);
        }

        .chevronRight {
            width: 12px;
            height: auto;
            transform: rotate(-90deg);
        }

        .doubleChevronLeft {
            width: 12px;
            height: auto;
            transform: rotate(90deg);
        }

        .doubleChevronRight {
            width: 12px;
            height: auto;
            transform: rotate(-90deg);
        }
    }
}

// Page Size Controls
.pageSizeContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;

    .pageSizeText {
        color: $primaryColor;
        font-size: 14px;
        font-weight: normal;
    }

    .pageSizeBox {
        width: 30px;
        height: 30px;
        background-color: $primaryColor;
        color: $white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        cursor: pointer;
        border-radius: $tinyBorderRadius;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: $secondaryColor;
        }
    }
}

// Page Size Modal Content
.pageSizeModalContent {
    display: flex;
    flex-direction: column;
    min-width: 80px;

    .pageSizeOption {
        padding: 14px 16px;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover:not(.currentPageSize) {
            background-color: rgba($primaryColor, 0.1);
        }

        &.currentPageSize {
            opacity: 0.6;
            cursor: default;
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 115px;
        height: 36px;
        font-size: 16px;

        .buttonIcon {
            width: 17px !important;
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 95px;
        height: 30px;
        padding: 0 10px;
        font-size: 13px;
        outline-width: $tinyBorderWidth;

        &:hover {
            outline-width: $smallBorderWidth;
        }
        &.buttonSelected {
            outline-width: $smallBorderWidth;
        }
        .buttonIcon {
            width: 13px !important;
            margin-left: 0 !important;
            margin-right: -2px !important;
        }
    }
}