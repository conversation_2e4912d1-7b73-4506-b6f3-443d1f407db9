SELECT "upd"."Id"
      ,"upd"."UserId"
      ,"user"."FirstName" + ' ' + "user"."LastName" "__User"
      ,"upd"."Code"
      ,"upd"."Value"
FROM public."UserPersistedData" "upd"
INNER JOIN public."User" "user" ON "upd"."UserId" = "user"."Id"
WHERE 1=1
ORDER BY "user"."LastName", "user"."FirstName", "upd"."Code";

-- Delete
-- DELETE FROM public."UserPersistedData"
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
-- AND "Code" = 'code_value'

-- Insert
-- INSERT INTO public."UserPersistedData" ("Id", "UserId", "Code", "Value")
-- VALUES ('xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx', 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx', 'code_value', 'value_text')
