"use client"

import styles from "./page.module.scss";
import { JC_Utils, JC_Utils_NGrave } from "../Utils";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import { useState } from "react";

export default function Page_Tools() {
    const [outputText, setOutputText] = useState<string | null>(null);

    // Handle format list button click
    const handleFormatList = async () => {
        // Play a random sound
        JC_Utils.playRandomSound();

        // Format client data
        const result = await JC_Utils_NGrave.formatClientData();
        setOutputText(result);
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Tools", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer maxWidth={1200} hasBorder={false}>
                <div className={styles.headerContainer}>
                    <JC_Title title="Tools" />
                </div>

                <div className={styles.toolsContainer}>
                    <JC_Button
                        text="Format List"
                        onClick={handleFormatList}
                        isSecondary
                        overrideClass={styles.formatListButton}
                    />

                    {/* Output Section */}
                    <div className={styles.outputSection}>
                        <JC_Title title="Output" isSecondary overrideClass={styles.outputTitle} />
                        <div className={`${styles.outputBox} ${outputText ? styles.hasContent : ''}`}>
                            {outputText ? (
                                <pre>{outputText}</pre>
                            ) : (
                                <div className={styles.placeholderText}>Formatted output will appear here</div>
                            )}
                        </div>
                    </div>

                    {/* Excel Upload Button */}
                    <JC_Button
                        text="Excel Upload"
                        linkToPage="excelUpload"
                        overrideClass={styles.excelUploadButton}
                    />
                </div>
            </JC_PageContentContainer>
        </div>
    );
}
