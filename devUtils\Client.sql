SELECT "client"."Id"
      ,"client"."Name"
      ,"client"."CreatedAt"
      ,"client"."ModifiedAt"
      ,"client"."Deleted"
FROM public."Client" "client"
WHERE 1=1
      AND "client"."Deleted" = 'False'
ORDER BY "client"."Name";

-- Delete
-- UPDATE public."Client"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."Client"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
