import { sql } from "@vercel/postgres";
import { UserPersistedDataModel } from "../../models/UserPersistedData";

export class UserPersistedDataBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id:string, code:string) {
        return (await sql<UserPersistedDataModel>`
            SELECT "Id",
                   "UserId",
                   "Code",
                   "Value"
            FROM public."UserPersistedData"
            WHERE "Id" = ${id}
              AND "Code" = ${code}
        `).rows[0];
    }

    static async GetAll() {
        return (await sql<UserPersistedDataModel>`
            SELECT "Id",
                   "UserId",
                   "Code",
                   "Value"
            FROM public."UserPersistedData"
            ORDER BY "UserId", "Code"
        `).rows;
    }

    static async GetByUserId(userId:string) {
        return (await sql<UserPersistedDataModel>`
            SELECT "Id",
                   "UserId",
                   "Code",
                   "Value"
            FROM public."UserPersistedData"
            WHERE "UserId" = ${userId}
            ORDER BY "Code"
        `).rows;
    }

    static async GetByUserIdAndCode(userId:string, code:string) {
        return (await sql<UserPersistedDataModel>`
            SELECT "Id",
                   "UserId",
                   "Code",
                   "Value"
            FROM public."UserPersistedData"
            WHERE "UserId" = ${userId}
              AND "Code" = ${code}
        `).rows[0];
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data:UserPersistedDataModel) {
        await sql`
            INSERT INTO public."UserPersistedData"
            (
                "Id",
                "UserId",
                "Code",
                "Value"
            )
            VALUES
            (
                ${data.Id},
                ${data.UserId},
                ${data.Code},
                ${data.Value}
            )
        `;
    }

    static async CreateList(dataList:UserPersistedDataModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data:UserPersistedDataModel) {
        await sql`
            UPDATE public."UserPersistedData"
            SET "Value" = ${data.Value}
            WHERE "Id" = ${data.Id}
              AND "Code" = ${data.Code}
        `;
    }

    static async UpdateList(dataList:UserPersistedDataModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id:string, code:string) {
        await sql`
            DELETE FROM public."UserPersistedData"
            WHERE "Id" = ${id}
              AND "Code" = ${code}
        `;
    }

    static async DeleteByUserId(userId:string) {
        await sql`
            DELETE FROM public."UserPersistedData"
            WHERE "UserId" = ${userId}
        `;
    }

    static async DeleteList(idCodePairs:{id:string, code:string}[]) {
        for (const pair of idCodePairs) {
            await this.Delete(pair.id, pair.code);
        }
    }
}
