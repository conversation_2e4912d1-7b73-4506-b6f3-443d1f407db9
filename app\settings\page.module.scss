@import '../global';

.mainContainer {
    @include mainContainerStyles;
    padding-top: 40px;
    justify-content: center;
    align-items: center;
    position: relative;

    .tilesGrid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 40px;
        width: 100%;
        max-width: 800px;
        // Offset the tiles upwards to compensate for breadcrumbs
        // so tiles maintain same vertical position as home page
        margin-top: -50px;

        .tile {
            width: 150px;
            height: 150px;
            background-color: $offBlack;
            border: 2px solid $primaryColor;
            border-radius: $smallBorderRadius;
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s ease;
            @include containerShadow;

            &:hover {
                transform: scale(1.05);
            }

            .tileContent {
                color: $primaryColor;
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                font-family: var(--font-k2d);
            }
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        .tilesGrid {
            gap: 30px;

            .tile {
                width: 130px;
                height: 130px;

                .tileContent {
                    font-size: 18px;
                }
            }
        }
    }
}

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .tilesGrid {
            gap: 25px;

            .tile {
                width: 120px;
                height: 120px;

                .tileContent {
                    font-size: 16px;
                }
            }
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        .tilesGrid {
            gap: 20px;

            .tile {
                width: 100px;
                height: 100px;

                .tileContent {
                    font-size: 14px;
                }
            }
        }
    }
}
