@import "../../../global";

.mainContainer {
  @include editPageMainContainerStyles;

  .dropdown {
    width: 100%;
    padding: 10px;
    background-color: $offBlack;
    color: $secondaryColor;
    border: 1px solid $primaryColor;
    border-radius: $tinyBorderRadius;
    font-size: 16px;
    cursor: pointer;

    &:focus {
      outline: none;
      border-color: lighten($primaryColor, 10%);
    }

    option {
      background-color: $offBlack;
      color: $secondaryColor;
    }
  }

  .editPageThreeDotsMenu {
    @include editPageThreeDotsMenu;
  }

  .headerContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
  }

  .formContainer {
    margin-top: 20px;
    width: 100%;
  }

  .noBottomMargin {
    margin-bottom: 0;
  }

  .productsContainer {
    margin-top: 30px;
  }

  .filterCheckboxesContainer {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    justify-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: $tinyBorderRadius;
    background-color: rgba($offBlack, 0.3);
    border: 1px solid rgba($primaryColor, 0.2);
  }

  .methodsContainer {
    width: 100%;
    margin-top: 15px;

    .methodsLabel {
      display: block;
      font-weight: bold;
      color: $white;
      text-align: center;
    }
  }

  .dimensionsContainer {
    width: 100%;
    display: flex;
    column-gap: 20px;
    align-items: flex-end;
    justify-content: center;

    .dimensionX {
      position: relative;

      .dimensionSeparator {
        position: absolute;
        right: -17px;
        bottom: 10px;
        color: $secondaryColor;
        font-weight: bold;
        font-size: 18px;
      }
    }

    .areaField {
      width: max-content !important;
      margin: 0 auto !important;
    }

    .hideOnTiny {
      display: block;
    }
  }

  .priceFieldsRow,
  .profitFinalRow {
    display: flex;
    flex-direction: row;
    column-gap: 10px;
    align-items: flex-start;
    width: 100%;
    justify-content: center;
    > div {
      width: 110px;
    }
  }

  .dropdownField {
    width: max-content !important;
    margin: 0 auto !important;

    label {
      text-align: center;
    }
  }

  .infoContainer {
    margin-top: -18px;
    padding: 10px;
    border-radius: $tinyBorderRadius;
    background-color: rgba($offBlack, 0.5);
    color: $white;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: center;
    width: 100%;
    max-width: 400px;
    box-sizing: border-box;

    &:hover {
      background-color: rgba($primaryColor, 0.1);
    }

    .separator {
      color: $primaryColor;
      font-weight: bold;
      margin: 0 8px;
    }
  }

  .materialCell,
  .methodCell {
    position: relative;
    cursor: pointer;
  }

  .tooltipContent {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 12px 16px;
    border-radius: $tinyBorderRadius;
    background-color: rgba($offBlack, 0.5);
    color: $white;
    text-align: center;

    .separator {
      margin: 0 8px;
      opacity: 0.6;
      color: $primaryColor;
      font-weight: bold;
    }
  }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
  .mainContainer {
    .headerContainer {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .priceFieldsRow,
    .profitFinalRow {
      flex-direction: column;
      row-gap: 15px;
      align-items: center;

      > div {
        width: 100%;
        max-width: 200px;
      }
    }

    .profitFinalRow {
      margin-top: 10px;
    }

    .filterCheckboxesContainer {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }
  }
}

@media (max-width: $tinyScreenSize) {
  .mainContainer {
    .dimensionsContainer {
      .hideOnTiny {
        display: none !important;
      }
    }

    .filterCheckboxesContainer {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      padding: 10px;
    }
  }
}
