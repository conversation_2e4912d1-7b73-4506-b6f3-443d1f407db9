import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { AddressModel } from "@/app/models/Address";
import { AddressBusiness } from "./business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" or "UserId" or get all
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const userId = params.get("userId");

        let result;
        if (id) {
            result = await JC_Utils_Business.sqlGet(AddressModel, id);
        } else if (userId) {
            result = await AddressBusiness.GetByUserId(userId);
        } else {
            result = await JC_Utils_Business.sqlGetList(AddressModel, undefined, {
                PageSize: undefined,
                PageIndex: undefined,
                Sorts: [{ SortField: "CreatedAt", SortAsc: false }]
            });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - CREATE - //
// ---------- //

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const address:AddressModel = await request.json();
        await JC_Utils_Business.sqlCreate(AddressModel, address);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - UPDATE - //
// ---------- //

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const address:AddressModel = await request.json();
        await JC_Utils_Business.sqlUpdate(AddressModel, address);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - DELETE - //
// ---------- //

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        await JC_Utils_Business.sqlDelete(AddressModel, id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});