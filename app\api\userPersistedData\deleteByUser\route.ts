import { NextRequest, NextResponse } from "next/server";
import { UserPersistedDataBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId");

        if (!userId) {
            return NextResponse.json({ error: "Missing 'userId' parameter" }, { status: 400 });
        }

        await UserPersistedDataBusiness.DeleteByUserId(userId);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
