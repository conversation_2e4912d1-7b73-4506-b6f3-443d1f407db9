import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ProductModel } from "@/app/models/Product";
import { ProductBusiness } from "./business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" or get all
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const clientId = params.get("clientId");

        let result;
        if (id) {
            result = await ProductBusiness.Get(id);
        } else if (clientId) {
            result = await ProductBusiness.GetByClientId(clientId);
        } else {
            result = await ProductBusiness.GetAll();
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - CREATE - //
// ---------- //

export const PUT = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const product:ProductModel = await request.json();
        await JC_Utils_Business.sqlCreate(ProductModel, product);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - UPDATE - //
// ---------- //

export const POST = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const product:ProductModel = await request.json();
        await JC_Utils_Business.sqlUpdate(ProductModel, product);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});


// ---------- //
// - DELETE - //
// ---------- //

export const DELETE = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        await JC_Utils_Business.sqlDelete(ProductModel, id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});