"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../../../../components/JC_Title/JC_Title";
import JC_Form from "../../../../components/JC_Form/JC_Form";
import JC_Field from "../../../../components/JC_Field/JC_Field";
import <PERSON><PERSON>_Spinner from "../../../../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../../../../components/JC_Breadcrumbs/JC_Breadcrumbs";
import JC_PageContentContainer from "../../../../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_ThreeDotsMenu from "../../../../components/JC_ThreeDotsMenu/JC_ThreeDotsMenu";
import JC_ModalConfirmation from "../../../../components/JC_ModalConfirmation/JC_ModalConfirmation";
import { MethodModel } from "../../../../models/Method";
import { JC_Get } from "../../../../apiServices/JC_Get";
import { JC_Utils, JC_Utils_Pricing } from "../../../../Utils";
import { FieldTypeEnum } from "../../../../enums/FieldType";
import { JC_ConfirmationModalUsageModel } from "../../../../models/ComponentModels/JC_ConfirmationModalUsage";

export default function Page_EditMethod({ params }: { params: { id: string } }) {
    const router = useRouter();
    const isNew = params.id === "new";

    const [method, setMethod] = useState<MethodModel>(new MethodModel());
    const [initialised, setInitialised] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>("");

    // Confirmation modal states
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>(null);
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);

    // Field states
    const [name, setName] = useState<string>("");
    const [costPrice, setCostPrice] = useState<number>(0);
    const [percentMarkup, setPercentMarkup] = useState<number>(0);
    const [salesPrice, setSalesPrice] = useState<number>(0);
    const [costPerSecond, setCostPerSecond] = useState<number>(0);
    const [isPercentMarkupReadOnly, setIsPercentMarkupReadOnly] = useState<boolean>(false);
    const [isSalesPriceReadOnly, setIsSalesPriceReadOnly] = useState<boolean>(true);

    // Load method on page load
    useEffect(() => {
        const loadData = async () => {
            try {
                // If editing existing method, load it
                if (!isNew) {
                    const methodResult = await JC_Get<MethodModel>(
                        MethodModel,
                        MethodModel.apiRoute,
                        { id: params.id }
                    );

                    setMethod(methodResult);
                    setName(methodResult.Name);
                    setCostPrice(methodResult.CostPrice);
                    setCostPerSecond(methodResult.CostPerSecond);

                    // Set field values
                    setPercentMarkup(methodResult.PercentMarkup);
                    setSalesPrice(methodResult.SalesPrice);

                    // Set readonly states based on IsSalesPriceReadOnlyOn flag
                    setIsPercentMarkupReadOnly(methodResult.IsSalesPriceReadOnlyOn);
                    setIsSalesPriceReadOnly(!methodResult.IsSalesPriceReadOnlyOn);

                    // Ensure both fields have values
                    calculatePriceFields(
                        methodResult.CostPrice,
                        methodResult.PercentMarkup,
                        methodResult.SalesPrice,
                        methodResult.IsSalesPriceReadOnlyOn
                    );
                } else {
                    // For new methods, initialize with default values
                    const defaultCostPrice = 0;
                    const defaultPercentMarkup = 0;

                    setCostPrice(defaultCostPrice);
                    setPercentMarkup(defaultPercentMarkup);

                    // Default to percent markup editable, sales price read-only
                    setIsPercentMarkupReadOnly(false);
                    setIsSalesPriceReadOnly(true);

                    // Calculate sales price based on default values
                    calculatePriceFields(
                        defaultCostPrice,
                        defaultPercentMarkup,
                        0,
                        false
                    );
                }

                setInitialised(true);
            } catch (error) {
                console.error("Error loading data:", error);
                JC_Utils.showToastError("Failed to load data.");
                setInitialised(true);
            }
        };

        loadData();
    }, [isNew, params.id]);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);
            setErrorMessage("");

            // Update method object with form values
            method.Name = name;
            method.CostPrice = costPrice;
            method.CostPerSecond = costPerSecond;

            // Set both fields with their current values
            method.PercentMarkup = percentMarkup;
            method.SalesPrice = salesPrice;

            // Set the IsSalesPriceReadOnlyOn flag based on which field is readonly
            method.IsSalesPriceReadOnlyOn = isPercentMarkupReadOnly;

            // Create or update method
            if (isNew) {
                await MethodModel.Create(method);
                JC_Utils.showToastSuccess("Method created successfully.");
            } else {
                await MethodModel.Update(method);
                JC_Utils.showToastSuccess("Method updated successfully.");
            }

            // Navigate back to methods list
            router.push("/settings/methods");
        } catch (error) {
            console.error("Error saving method:", error);
            setErrorMessage("Failed to save method.");
            setIsLoading(false);
        }
    };

    // Calculate price fields based on which field is editable
    const calculatePriceFields = (
        currentCostPrice: number,
        currentPercentMarkup: number,
        currentSalesPrice: number,
        isPercentMarkupFieldReadOnly: boolean
    ) => {
        if (isPercentMarkupFieldReadOnly) {
            // PercentMarkup is readonly, SalesPrice is editable
            // Calculate PercentMarkup from CostPrice and SalesPrice
            if (currentCostPrice > 0) {
                const calculatedPercentMarkup = JC_Utils_Pricing.calculatePercentMarkup(
                    currentCostPrice,
                    currentSalesPrice
                );
                setPercentMarkup(calculatedPercentMarkup);
            } else {
                // If CostPrice is 0, set default PercentMarkup
                setPercentMarkup(0);
            }
        } else {
            // SalesPrice is readonly, PercentMarkup is editable
            // Calculate SalesPrice from CostPrice and PercentMarkup
            const calculatedSalesPrice = JC_Utils_Pricing.calculateSalesPrice(
                currentCostPrice,
                currentPercentMarkup
            );
            setSalesPrice(calculatedSalesPrice);
        }
    };

    // Toggle between percent markup and sales price
    const togglePriceField = (isPercentMarkupField: boolean) => {
        // Only toggle if the clicked field is readonly
        if (isPercentMarkupField && isPercentMarkupReadOnly) {
            // Switch to percent markup editable
            setIsPercentMarkupReadOnly(false);
            setIsSalesPriceReadOnly(true);

            // Calculate fields with new readonly settings
            calculatePriceFields(costPrice, percentMarkup, salesPrice, false);
        } else if (!isPercentMarkupField && isSalesPriceReadOnly) {
            // Switch to sales price editable
            setIsPercentMarkupReadOnly(true);
            setIsSalesPriceReadOnly(false);

            // Calculate fields with new readonly settings
            calculatePriceFields(costPrice, percentMarkup, salesPrice, true);
        }
    };

    // Handle cost price change
    const handleCostPriceChange = (value: string) => {
        const newCostPrice = parseFloat(value) || 0;
        setCostPrice(newCostPrice);

        // Recalculate the appropriate field based on which is readonly
        calculatePriceFields(newCostPrice, percentMarkup, salesPrice, isPercentMarkupReadOnly);
    };

    // Handle percent markup change
    const handlePercentMarkupChange = (value: string) => {
        const newPercentMarkup = parseFloat(value) || 0;
        setPercentMarkup(newPercentMarkup);

        // Update sales price based on new percent markup
        calculatePriceFields(costPrice, newPercentMarkup, salesPrice, false);
    };

    // Handle sales price change
    const handleSalesPriceChange = (value: string) => {
        const newSalesPrice = parseFloat(value) || 0;
        setSalesPrice(newSalesPrice);

        // Update percent markup based on new sales price
        calculatePriceFields(costPrice, percentMarkup, newSalesPrice, true);
    };

    // Handle cost per second change
    const handleCostPerSecondChange = (value: string) => {
        const newCostPerSecond = parseFloat(value) || 0;
        setCostPerSecond(newCostPerSecond);
    };

    // Handle delete method
    const handleDeleteMethod = () => {
        if (isNew) return; // Don't show delete option for new methods

        setConfirmationModalData({
            title: "Delete Method",
            text: `Are you sure you want to delete "${name}"?`,
            submitButtons: [{
                text: "Delete",
                onSubmit: async () => {
                    try {
                        setConfirmationLoading(true);
                        await MethodModel.Delete(params.id);
                        setConfirmationLoading(false);
                        setConfirmationModalData(null);
                        JC_Utils.showToastSuccess("Method deleted successfully.");
                        router.push("/settings/methods");
                    } catch (error) {
                        console.error("Error deleting method:", error);
                        setConfirmationLoading(false);
                        JC_Utils.showToastError("Failed to delete method.");
                    }
                }
            }]
        });
    };

    return (
        <div className={styles.mainContainer}>
            <JC_Breadcrumbs
                items={[
                    { label: "Home", path: "/" },
                    { label: "Settings", path: "/settings" },
                    { label: "Methods", path: "/settings/methods" },
                    { label: isNew ? "New Method" : "Edit Method", path: "", isCurrent: true }
                ]}
            />
            <JC_PageContentContainer hasBorder={true}>
                <div className={styles.headerContainer}>
                    <JC_Title title={isNew ? "New Method" : "Edit Method"} />
                </div>

                {!initialised ? (
                    <JC_Spinner isPageBody />
                ) : (
                    <div className={styles.formContainer}>
                        <JC_Form
                            submitButtonText="Save"
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                {
                                    inputId: "name",
                                    type: FieldTypeEnum.Text,
                                    label: "Name",
                                    value: name,
                                    onChange: (value) => setName(value),
                                    validate: (value) =>
                                        JC_Utils.stringNullOrEmpty(value as string)
                                            ? "Name is required"
                                            : ""
                                },
                                {
                                    inputId: "price-fields",
                                    type: FieldTypeEnum.Custom,
                                    customNode: (
                                        <div className={styles.priceFieldsRow}>
                                            <JC_Field
                                                inputId="costPrice"
                                                type={FieldTypeEnum.Number}
                                                label="Cost ($/m²)"
                                                value={costPrice}
                                                onChange={handleCostPriceChange}
                                                decimalPlaces={2}
                                            />
                                            <JC_Field
                                                inputId="percentMarkup"
                                                type={FieldTypeEnum.Number}
                                                label="Markup (%)"
                                                value={percentMarkup}
                                                onChange={handlePercentMarkupChange}
                                                decimalPlaces={2}
                                                readOnly={isPercentMarkupReadOnly}
                                                onClick={() => togglePriceField(true)}
                                                inputOverrideClass={isPercentMarkupReadOnly ? styles.readOnlyToggle : ""}
                                            />
                                            <JC_Field
                                                inputId="salesPrice"
                                                type={FieldTypeEnum.Number}
                                                label="Sale ($)"
                                                value={salesPrice}
                                                onChange={handleSalesPriceChange}
                                                decimalPlaces={2}
                                                readOnly={isSalesPriceReadOnly}
                                                onClick={() => togglePriceField(false)}
                                                inputOverrideClass={isSalesPriceReadOnly ? styles.readOnlyToggle : ""}
                                            />
                                        </div>
                                    )
                                },
                                {
                                    inputId: "costPerSecond",
                                    type: FieldTypeEnum.Number,
                                    label: "Cost ($/second)",
                                    value: costPerSecond,
                                    onChange: handleCostPerSecondChange,
                                    decimalPlaces: 2
                                }
                            ]}
                        />
                    </div>
                )}
            </JC_PageContentContainer>

            {/* Confirmation Modal */}
            {confirmationModalData && (
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />
            )}
        </div>
    );
}
