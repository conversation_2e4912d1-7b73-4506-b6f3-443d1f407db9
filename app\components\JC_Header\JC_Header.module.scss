@import '../../global';

// Header
.mainContainer {
    margin: auto;
    width: 100%;
    height: max-content;
    padding: 20px 40px 20px 40px;
    box-sizing: border-box;
    background-color: $offBlack;
    border-bottom: solid $smallBorderWidth $primaryColor;
    z-index: 1;

    // Logo + Account
    .logoAccountContainer {
        margin: auto;
        max-width: 800px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .logo {
            margin-top: -10px;
            width: 380px;
            height: auto;
            max-height: 400px;
            box-sizing: border-box;
            cursor: pointer;
            opacity: 0.9;
            &:hover {
                opacity: 1;
            }

            &.nonClickable {
                cursor: default;
                opacity: 1;
                &:hover {
                    opacity: 1;
                }
            }
        }

        // Checkout + Login/Register (used in 2 places)
        .checkoutAccountContainer {
            width: max-content;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            column-gap: 40px;

            // Login/Register
            .loginRegisterContainer {
                width: max-content;
                height: max-content;
                cursor: pointer;

                .loginRegisterText {
                    padding: 12px 0;
                    font-size: 25px;
                    font-weight: bold;
                }
                &:hover {
                    color: $primaryColor;
                }
            }

            &.tinyCheckoutAccount {
                display: none;
            }
        }
    }


}


// - SCREEN SIZES - //



@media (max-width: $tinyScreenSize) {
    .mainContainer {
        border-width: $largeBorderWidth;
        .logo {
            width: 240px !important;
            margin-bottom: 10px;
        }
        .logoAccountContainer {
            .checkoutAccountContainer {
                column-gap: 26px !important;
                .loginRegisterText {
                    font-size: 18px !important;
                }
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        .logo {
            width: 180px !important;
        }
        .logoAccountContainer {
            flex-direction: column;
            .checkoutAccountContainer {
                column-gap: 18px !important;
            }
        }

    }
}