@import '../../global';

.mainContainer {
    @include listPageMainContainerStyles;

    .headerContainer {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
    }

    .addButton {
        margin-top: 30px;
        div {
            margin-top: -3px;
        }
    }
}

.methodCell {
    position: relative;

    .methodCount {
        position: relative;
        z-index: 1;
    }
}

.methodTooltip {
    padding: 12px 14px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .methodName {
        white-space: nowrap;
        text-align: center;
        font-weight: bold;
    }
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        .headerContainer {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
    }
}
