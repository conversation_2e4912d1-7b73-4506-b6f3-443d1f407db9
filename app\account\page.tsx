"use client"

import styles from "./page.module.scss";
import React from "react";
import { useEffect, useState } from "react";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_ModalConfirmation from "../components/JC_ModalConfirmation/JC_ModalConfirmation";
import JC_Modal from "../components/JC_Modal/JC_Modal";
import JC_PasswordRequirements from "../components/JC_PasswordRequirements/JC_PasswordRequirements";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { signOut, useSession } from "next-auth/react";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { JC_ConfirmationModalUsageModel } from "../models/ComponentModels/JC_ConfirmationModalUsage";
import { UserModel } from "../models/User";
import { GlobalSettingsModel } from "../models/GlobalSettings";
import { FieldTypeEnum } from "../enums/FieldType";

import { JC_Utils, JC_Utils_Validation } from "../Utils";
import JC_PageContentContainer from "../components/JC_PageContentContainer/JC_PageContentContainer";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Breadcrumbs from "../components/JC_Breadcrumbs/JC_Breadcrumbs";


export default function Page_Account() {

    // - STATE - //

    const session = useSession();
    // Loading
    const [initialised, setInitialised] = useState<boolean>(false);
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [logoutLoading, setLogoutLoading] = useState<boolean>(false);
    // Account Details
    const [firstName, setFirstName] = useState<string>(session.data!.user.FirstName);
    const [lastName, setLastName] = useState<string>(session.data!.user.LastName);
    const [phone, setPhone] = useState<string>(session.data!.user.Phone ?? "");
    const [company, setCompany] = useState<string>(session.data!.user.CompanyName ?? "");
    const [emailPromotionsChecked, setEmailPromotionsChecked] = useState<boolean>(session.data!.user.IsEmailSubscribed);
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);

    // Password change modal state
    const [passwordModalOpen, setPasswordModalOpen] = useState<boolean>(false);
    const [currentPassword, setCurrentPassword] = useState<string>("");
    const [newPassword, setNewPassword] = useState<string>("");
    const [confirmNewPassword, setConfirmNewPassword] = useState<string>("");
    const [passwordChangeLoading, setPasswordChangeLoading] = useState<boolean>(false);
    const [passwordChangeError, setPasswordChangeError] = useState<string>("");
    const [submitClicked, setSubmitClicked] = useState<boolean>(false);


    // - INITIALISE - //

    useEffect(() => {
        // IF just updated details, show success
        if (localStorage.getItem("showUpdatedAccountDetailsSuccess") == "1") {
            JC_Utils.showToastSuccess("Your details have been updated!")
            localStorage.setItem("showUpdatedAccountDetailsSuccess", "0");
        }

        // Set initialised to true after loading
        setInitialised(true);
    }, []);


    // - HANDLES - //

    // Account Details
    async function saveAccountDetails() {
        setSaveLoading(true);
        let newUser:UserModel = {
            ...session.data!.user,
            FirstName: firstName,
            LastName: lastName,
            Phone: !JC_Utils.stringNullOrEmpty(phone) ? phone : undefined,
            CompanyName: !JC_Utils.stringNullOrEmpty(company) ? company : undefined,
            IsEmailSubscribed: emailPromotionsChecked
        };
        // Update db User
        await UserModel.Update(newUser);
        // Trigger "jwt()" callback to refresh User from db
        const refreshTokenSetting = new GlobalSettingsModel({
            Code: "ForceRefreshAuthToken",
            Description: "",
            Value: "1"
        });
        await JC_Post<GlobalSettingsModel>(GlobalSettingsModel, GlobalSettingsModel.apiRoute, refreshTokenSetting);
        // Update the session with new data (need this plus the update in "jwt()" callback to get update showing properly)
        const newSession = session.data;
        newSession!.user = newUser;
        await session.update(newSession);
        // Show success toast after refresh
        localStorage.setItem("showUpdatedAccountDetailsSuccess", "1");
        // Refresh
        setTimeout(() => window.location.reload(), 100);
    }

    // Reset Password - Open Modal
    function resetPassword() {
        setPasswordModalOpen(true);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
        setPasswordChangeError("");
        setSubmitClicked(false);
    }

    // Change Password
    async function changePassword() {
        setSubmitClicked(true);
        setPasswordChangeLoading(true);
        setPasswordChangeError("");

        try {
            await JC_PostRaw("user/changePassword", {
                userId: session.data!.user.Id,
                currentPassword: currentPassword,
                newPassword: newPassword
            });

            setPasswordModalOpen(false);
            JC_Utils.showToastSuccess("Password changed successfully!");

            // Reset form
            setCurrentPassword("");
            setNewPassword("");
            setConfirmNewPassword("");
            setSubmitClicked(false);

        } catch (error: any) {
            setPasswordChangeError(error.error || "An error occurred while changing password.");
        }

        setPasswordChangeLoading(false);
    }


    // - Main - //

    return (
        <React.Fragment>
            {/* Main Container */}
            <div className={styles.mainContainer}>
                <JC_Breadcrumbs
                    items={[
                        { label: "Home", path: "/" },
                        { label: "Account", path: "", isCurrent: true }
                    ]}
                />
                <JC_PageContentContainer>
                    <div className={styles.headerContainer}>
                        <JC_Title title="Account Details" />
                    </div>

                    {!initialised ? (
                        <JC_Spinner isPageBody />
                    ) : (
                        <div className={styles.accountDetailsContainer}>
                            <JC_Form
                                onSubmit={saveAccountDetails}
                                isDisabled={confirmationLoading || logoutLoading}
                                isLoading={saveLoading}
                                fields={[
                                // Email
                                {
                                    ...D_FieldModel_Email(),
                                    overrideClass: styles.fieldOverride,
                                    value: session.data!.user.Email,
                                    readOnly: true
                                },
                                // First Name
                                {
                                    ...D_FieldModel_FirstName(),
                                    overrideClass: styles.fieldOverride,
                                    value: firstName,
                                    onChange: (newValue) => setFirstName(newValue),
                                },
                                // Last Name
                                {
                                    ...D_FieldModel_LastName(),
                                    overrideClass: styles.fieldOverride,
                                    value: lastName,
                                    onChange: (newValue) => setLastName(newValue),
                                },
                                // Phone
                                {
                                    ...D_FieldModel_Phone(),
                                    overrideClass: styles.fieldOverride,
                                    value: phone,
                                    onChange: (newValue) => setPhone(newValue)
                                },
                                // Company Name
                                {
                                    inputId: "company-name-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Company (optional)",
                                    iconName: "User",
                                    value: company,
                                    onChange: (newValue) => setCompany(newValue)
                                }
                            ]}
                        />

                        <hr style={{ width: "80%", borderTopWidth: "2px", borderColor: "grey" }}/>

                        {/* Reset Password */}
                        <JC_Button
                            text="Reset Password"
                            onClick={resetPassword}
                            isDisabled={confirmationLoading || saveLoading || logoutLoading}
                        />

                        {/* Logout */}
                        <JC_Button
                            text="Logout"
                            onClick={() => { setLogoutLoading(true); signOut({ callbackUrl: "/loginRegister" }); }}
                            isDisabled={confirmationLoading || saveLoading}
                            isLoading={logoutLoading}
                        />
                    </div>
                    )}
                </JC_PageContentContainer>
            </div>

            {/* Confirmation */}
            {confirmationModalData &&
            <JC_ModalConfirmation
                width={confirmationModalData.width}
                title={confirmationModalData.title}
                text={confirmationModalData.text}
                isOpen={confirmationModalData != null}
                onCancel={() => setConfirmationModalData(null)}
                submitButtons={confirmationModalData.submitButtons}
                isLoading={confirmationLoading}
            />}

            {/* Password Change Modal */}
            <JC_Modal
                width="450px"
                title="Reset Password"
                isOpen={passwordModalOpen}
                onCancel={() => setPasswordModalOpen(false)}
            >
                <JC_Form
                    submitButtonText="Change Password"
                    onSubmit={changePassword}
                    isLoading={passwordChangeLoading}
                    errorMessage={passwordChangeError}
                    fields={[
                        // Current Password
                        {
                            inputId: "current-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Current Password",
                            onChange: (newValue) => setCurrentPassword(newValue),
                            value: currentPassword,
                            validate: (v: any) => JC_Utils.stringNullOrEmpty(v) ? "Enter your current password." : ""
                        },
                        // New Password
                        {
                            inputId: "new-password-input",
                            type: FieldTypeEnum.Password,
                            label: "New Password",
                            onChange: (newValue) => setNewPassword(newValue),
                            value: newPassword,
                            validate: (v: any) => JC_Utils.stringNullOrEmpty(v)
                                ? "Enter a password."
                                : !JC_Utils_Validation.validPassword(v)
                                    ? "Password invalid."
                                    : ""
                        },
                        // Password Requirements
                        {
                            inputId: "password-requirements",
                            type: FieldTypeEnum.Custom,
                            customNode: <JC_PasswordRequirements
                                key="password-requirements"
                                password={newPassword}
                                showErrors={submitClicked}
                            />
                        },
                        // Confirm New Password
                        {
                            inputId: "confirm-new-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Confirm New Password",
                            onChange: (newValue) => setConfirmNewPassword(newValue),
                            value: confirmNewPassword,
                            validate: (v: any) => JC_Utils.stringNullOrEmpty(v)
                                ? "Confirm the password."
                                : confirmNewPassword !== newPassword
                                    ? "Passwords do not match"
                                    : ""
                        }
                    ]}
                />
            </JC_Modal>
        </React.Fragment>
    );
}
