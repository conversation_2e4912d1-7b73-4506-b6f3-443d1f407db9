SELECT "material"."Id"
      ,"material"."Name"
      ,"material"."MethodIdsListJson"
      ,(
        SELECT STRING_AGG("method"."Name", ', ')
        FROM public."Method" "method"
        WHERE "method"."Deleted" = 'False'
          AND "method"."Id"::text IN (
            SELECT jsonb_array_elements_text(CAST("material"."MethodIdsListJson" AS jsonb))
          )
      ) AS "__MethodNames"
      ,"material"."CostPrice"
      ,"material"."PercentMarkup"
      ,"material"."SalesPrice"
      ,"material"."IsSalesPriceReadOnlyOn"
      ,"material"."CreatedAt"
      ,"material"."ModifiedAt"
      ,"material"."Deleted"
FROM public."Material" "material"
WHERE 1=1
      AND "material"."Deleted" = 'False'
ORDER BY "material"."Name";

-- Delete
-- UPDATE public."Material"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'

-- Un-delete
-- UPDATE public."Material"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
