import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { AddressBusiness } from "../business";
import { JC_Utils_Auth } from "@/app/utils/authUtils";

export const dynamic = 'force-dynamic';

// Get Addresses by UserId
export const GET = JC_Utils_Auth.withApiAuth(async (request: NextRequest) => {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId");

        if (!userId) {
            return NextResponse.json({ error: "Missing 'userId' parameter" }, { status: 400 });
        }

        const result = await AddressBusiness.GetByUserId(userId);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
});
